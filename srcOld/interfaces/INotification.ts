import {Timestamp} from '@react-native-firebase/firestore';

export interface INotification {
  sentTo: string[];
  message: INotificationMessage;
  userId: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isRead: boolean;
  id?: string;
}

export interface INotificationMessage {
  tokens: string[];
  notification: {
    title: string;
    body: string;
  };
  data: {
    taskId: string;
  };
}
