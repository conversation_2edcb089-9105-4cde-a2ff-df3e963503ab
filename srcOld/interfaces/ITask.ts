import {Timestamp} from '@react-native-firebase/firestore';
import {IUser} from './IUser';

export interface ITask {
  assignedTo: string[];
  createdAt: Timestamp;
  date: Timestamp;
  time: Timestamp;
  updatedAt: Timestamp;
  details: string;
  incentive: number;
  link: string;
  attachmentUrl: string;
  attachmentType: 'pdf' | 'image';
  title: string;
  id: string;
  status: 'pending' | 'done';
  users?: IUser[];
}
