import {StyleSheet} from 'react-native';
import {Colors, Fonts} from '../../utilities/theme/theme';

export const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginTop: 12,
    width: '100%',
  },
  sideBar: {
    width: 11.42,
    height: '100%',
  },
  contentContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingRight: 16,
    width: '97%',
    alignItems: 'center',
    paddingVertical: 15,
  },
  textContainer: {
    paddingLeft: 13,
    width: '80%',
  },
  taskTitle: {
    fontFamily: Fonts.Medium,
    fontSize: 14,
    lineHeight: 21,
  },
  taskTime: {
    fontFamily: Fonts.Regular,
    fontSize: 12,
    lineHeight: 21,
  },
  avatar: {
    height: 28,
    width: 28,
  },
  textPersent: {
    color: Colors.primary,
    fontSize: 5,
    fontFamily: Fonts.Semibold,
  },
  completeContainer: {
    flexDirection: 'row',
    marginTop: 20,
  },

  CompText: {
    fontSize: 9,
    fontFamily: Fonts.Medium,
    color: Colors.primary,
  },
  CompImg: {
    width: 16,
    height: 13,
    marginLeft: 5,
  },
});
