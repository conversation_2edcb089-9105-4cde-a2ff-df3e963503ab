import React from 'react';
import {
  Text,
  StyleSheet,
  View,
  Image,
  TextInputProps,
  ImageSourcePropType,
  ViewStyle,
} from 'react-native';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {FC} from 'react';
import moment from 'moment';
import {Timestamp} from '@react-native-firebase/firestore';

interface Props extends TextInputProps {
  title?: string;
  lefticon?: ImageSourcePropType;
  containerStyle?: ViewStyle;
  body: string;
  time: Timestamp;
}

const NotificationComp: FC<Props> = ({
  lefticon,
  title,
  containerStyle,
  body,
  time,
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <Image source={lefticon} style={styles.lefticon} resizeMode="contain" />
      <View style={styles.innerContainer}>
        <View style={styles.textContainer}>
          <Text style={styles.mainTitle} numberOfLines={1}>
            {title}
          </Text>
        </View>
        <Text style={styles.subTitle} numberOfLines={1}>
          {body}
        </Text>
      </View>
      <View style={styles.timeContainer}>
        <Text style={styles.timeText}>
          {moment(time?.toDate()).format('hh:mm')}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.background,
    flexDirection: 'row',
    paddingVertical: 2,
    marginTop: 8,
  },
  innerContainer: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    flex: 1,
  },
  lefticon: {
    width: 36,
    height: 36,
    marginRight: 14,
  },
  mainTitle: {
    fontSize: 14,
    fontFamily: Fonts.Semibold,
    color: Colors.primary,
    lineHeight: 19,
    width: '90%',
  },
  commentText: {
    fontSize: 12,
    paddingRight: 24,
    lineHeight: 19,
  },
  timeText: {
    color: Colors.primary,
    fontSize: 8,
    fontFamily: Fonts.Semibold,
  },
  subTitle: {
    fontSize: 14,
    fontFamily: Fonts.Regular,
    color: Colors.buttonbgcolor,
    lineHeight: 19,
  },
  textContainer: {
    flexDirection: 'row',
  },
  timeContainer: {
    alignSelf: 'center',
  },
});

export default NotificationComp;
