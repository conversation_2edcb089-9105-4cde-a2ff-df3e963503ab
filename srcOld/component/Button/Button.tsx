import {
  Text,
  StyleSheet,
  View,
  TouchableOpacity,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
} from 'react-native';
import {Colors, Fonts} from '../../utilities/theme/theme';

export const Button = ({
  title,
  onPress,
  containerStyle,
  textContainer,
  isLoading,
  disabled,
}: {
  title: string;
  onPress?: () => void;
  containerStyle?: ViewStyle;
  textContainer?: TextStyle;
  isLoading?: boolean;
  disabled?: boolean;
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      style={[
        styles.Button,
        {
          backgroundColor: disabled ? Colors.lightcolor : Colors.buttonbgcolor,
        },
        containerStyle,
      ]}>
      {isLoading ? (
        <ActivityIndicator color={'white'} size={'small'} />
      ) : (
        <Text style={[styles.Buttontext, textContainer]}>{title}</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  Button: {
    height: 45,
    backgroundColor: Colors.buttonbgcolor,
    alignItems: 'center',
    justifyContent: 'center',
  },

  Buttontext: {
    color: Colors.secondary,
    fontSize: 16,
    fontFamily: Fonts.Semibold,
  },
});
