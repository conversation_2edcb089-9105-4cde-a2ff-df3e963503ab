import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import Modal from 'react-native-modal';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {SuccessTick} from '../../assets/svgIcons';
import {ms} from 'react-native-size-matters';

interface SuccessModalProps {
  isVisible: boolean;
  onClose: () => void;
  message?: string;
}

const SuccessModal: React.FC<SuccessModalProps> = ({
  isVisible,
  onClose,
  message = 'TASK ADDED',
}) => {
  return (
    <Modal isVisible={isVisible} onBackdropPress={onClose}>
      <View style={styles.successContainer}>
        <SuccessTick />
        <Text style={styles.successText}>{message}</Text>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  successText: {
    fontFamily: Fonts.Semibold,
    fontSize: ms(20),
    color: '#27AE60',
    marginTop: 22,
  },
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 50,
    paddingHorizontal: 89,
    backgroundColor: Colors.white,
    borderRadius: 16,
  },
});

export default SuccessModal;
