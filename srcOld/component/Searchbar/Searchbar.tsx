import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  TextInput,
  View,
  TextInputProps,
  ViewStyle,
} from 'react-native';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {FC} from 'react';

interface Props extends TextInputProps {
  placeholder: string;
  RightIcon: ImageSourcePropType;
  containerStyle?: ViewStyle;
}

const Searchbar: FC<Props> = ({
  placeholder,
  RightIcon,
  containerStyle,
  ...rest
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <TextInput
        style={[styles.textinput]}
        textAlignVertical="center"
        placeholder={placeholder}
        placeholderTextColor={'#9E9E9E'}
        {...rest}
      />
      <Image source={RightIcon} style={styles.RightIconContainer} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 48,
    backgroundColor: Colors.white,
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 12,
  },
  RightIconContainer: {
    width: 18,
    height: 18,
  },

  textinput: {
    fontSize: 14,
    color: Colors.primary,
    fontFamily: Fonts.Regular,
    flex: 1,
  },
});

export default Searchbar;
