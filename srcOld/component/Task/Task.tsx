import {
  Image,
  StyleSheet,
  Text,
  View,
  TextInputProps,
  ViewStyle,
  TouchableOpacity,
  Dimensions,
  ScrollView,
} from 'react-native';
import {images} from '../../assets/images';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {FC} from 'react';
import {vs} from 'react-native-size-matters';
import {IUser} from '../../interfaces/IUser';

const screenWidth = Dimensions.get('window').width;
const itemWidth = screenWidth * 0.425;

interface Props extends TextInputProps {
  containerStyle?: ViewStyle;
  varient?: string;
  title: string;
  image?: IUser[];
  numText: number;
  onPress?: () => void;
}

const Task: FC<Props> = ({
  containerStyle,
  varient,
  title,
  image,
  numText,
  onPress,
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.container,
        containerStyle,
        {
          backgroundColor:
            varient == 'primary' ? Colors.buttonbgcolor : Colors.white,
        },
      ]}>
      <View style={styles.titleContainer}>
        <Text
          style={[
            styles.title,
            {color: varient == 'primary' ? Colors.white : Colors.primary},
          ]}
          numberOfLines={2}>
          {title}
        </Text>
        <View style={styles.starContainer}>
          <Image
            source={images.staricon}
            style={styles.staricon}
            resizeMode="contain"
          />
          <Text
            style={[
              styles.numtext,
              {color: varient == 'primary' ? Colors.white : Colors.primary},
            ]}>
            {numText}
          </Text>
        </View>
      </View>
      <View>
        <View style={styles.assignContainer}>
          <Text
            style={[
              styles.assignText,
              {color: varient == 'primary' ? Colors.white : Colors.primary},
            ]}>
            Assigned to
          </Text>
          <View>
            <ScrollView horizontal>
              {image?.map((img, index) => (
                <Image
                  key={index}
                  source={
                    img.profileImage
                      ? {uri: img.profileImage.url}
                      : images.avatarPlaceholder
                  }
                  style={[styles.assignicon, {marginLeft: index == 0 ? 0 : -5}]}
                />
              ))}
            </ScrollView>
          </View>
        </View>
        <View style={styles.Completecontaier}>
          <Text
            style={[
              styles.completeText,
              {color: varient == 'primary' ? Colors.white : Colors.primary},
            ]}>
            Completed
          </Text>
          <Text
            style={[
              styles.textpersent,
              {color: varient == 'primary' ? Colors.white : Colors.primary},
            ]}>
            100%
          </Text>
        </View>
        <View
          style={{
            height: 3,
            backgroundColor:
              varient == 'primary' ? Colors.green : Colors.buttonbgcolor,
            width: '100%',
            marginTop: 10,
            borderRadius: 6,
          }}
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    width: itemWidth,
    backgroundColor: Colors.buttonbgcolor,
    padding: 8,
    height: vs(120),
    justifyContent: 'space-between',
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 16,
    fontFamily: Fonts.Semibold,
    color: Colors.white,
    flex: 1,
  },
  starContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  staricon: {
    width: 29,
    height: 11,
    marginTop: 4,
  },
  numtext: {
    fontSize: 14,
    fontFamily: Fonts.Regular,
    color: Colors.white,
    marginLeft: -5,
  },
  assignContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  assignicon: {
    height: 20,
    width: 20,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: Colors.white,
    marginLeft: -5,
  },
  assignText: {
    fontSize: 10,
    color: Colors.white,
    fontFamily: Fonts.Regular,
  },
  Completecontaier: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 9,
    alignItems: 'center',
  },
  completeText: {
    fontSize: 8,
    fontFamily: Fonts.Regular,
    color: Colors.white,
    lineHeight: 12,
    // fontWeight: '400',
  },
  textpersent: {
    fontSize: 8,
    fontFamily: Fonts.Semibold,
    color: Colors.white,
  },
});

export default Task;
