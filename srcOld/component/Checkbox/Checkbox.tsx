import {StyleSheet, TouchableOpacity, ViewStyle} from 'react-native';
import React from 'react';
import {Colors} from '../../utilities/theme/theme';
import {TickIcon} from '../../assets/svgIcons';

interface props {
  onPress: () => void;
  isSelected: boolean;
  containerStyle?: ViewStyle;
}
const Checkbox: React.FC<props> = ({onPress, isSelected, containerStyle}) => {
  return (
    <TouchableOpacity
      style={[styles.container, containerStyle]}
      onPress={onPress}>
      {isSelected && <TickIcon />}
    </TouchableOpacity>
  );
};

export default Checkbox;

const styles = StyleSheet.create({
  container: {
    borderWidth: 2,
    height: 20,
    width: 20,
    borderColor: Colors.buttonbgcolor,
    marginTop: 18,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 6,
  },
});
