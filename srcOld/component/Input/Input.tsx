import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  TextInputProps,
  TextStyle,
  ViewStyle,
} from 'react-native';
import {images} from '../../assets/images';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {FC} from 'react';

interface Props extends TextInputProps {
  isPassword?: boolean;
  onLeftIconPress?: () => void;
  editIcon?: boolean;
  textinputStyles?: TextStyle;
  title?: string;
  placeholder?: string;
  secureTextEntry?: boolean;
  value?: string;
  leftIcon?: ImageSourcePropType;
  RightIcon?: ImageSourcePropType;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
  leftIconContainer?: ViewStyle;
  titleStyle?: TextStyle;
}

const Input: FC<Props> = ({
  value,
  secureTextEntry,
  leftIcon,
  title,
  isPassword,
  onRightIconPress,
  RightIcon,
  containerStyle,
  leftIconContainer,
  titleStyle,
  textinputStyles,
  ...rest
}) => {
  return (
    <View style={containerStyle}>
      {title ? (
        <Text style={[styles.titleStyle, titleStyle]}>{title}</Text>
      ) : null}
      <View style={[styles.container, textinputStyles]}>
        {leftIcon ? (
          <Image source={leftIcon} style={styles.leftIconContainer} />
        ) : null}
        <TextInput
          style={[styles.textinput]}
          textAlignVertical="center"
          placeholderTextColor={'#9E9E9E'}
          secureTextEntry={secureTextEntry}
          value={value}
          {...rest}
        />
        {isPassword ? (
          <TouchableOpacity onPress={onRightIconPress}>
            <Image
              source={secureTextEntry ? images.hideicon : images.unhideicon}
              style={[
                styles.leftIconContainer,
                {tintColor: value ? '#167738' : '#9E9E9E'},
              ]}
            />
          </TouchableOpacity>
        ) : RightIcon ? (
          <TouchableOpacity onPress={onRightIconPress} activeOpacity={0.7}>
            <Image
              source={RightIcon}
              style={styles.leftIconContainer}
              resizeMode="contain"
            />
          </TouchableOpacity>
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 48,
    backgroundColor: Colors.white,
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 8,
    paddingHorizontal: 12,
  },
  leftIconContainer: {
    width: 20,
    height: 20,
  },

  titleStyle: {
    fontSize: 14,
    fontFamily: Fonts.Regular,
    color: Colors.lightcolor,
  },
  textinput: {
    fontSize: 14,
    color: Colors.primary,
    fontFamily: Fonts.Regular,
    flex: 1,
    paddingLeft: 12,
  },
});

export default Input;
