import * as React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import Home from '../screens/appflow/Home/Home';
import BottomTabs from './BottomTabs';
import ProgressDetails from '../screens/appflow/ProgressDetails/ProgressDetails';
import {TouchableOpacity, Image} from 'react-native';
import {Colors, Fonts} from '../utilities/theme/theme';
import {images} from '../assets/images';
import EditProfile from '../screens/appflow/EditProfile/EditProfile';
import Notification from '../screens/appflow/Notification/Notification';
import ChangePassword from '../screens/appflow/ChangePassword/ChangePassword';
import UpdatedPassword from '../screens/appflow/UpdatedPassword/UpdatedPassword';
import Policy from '../screens/appflow/Policy/Policy';
import CompleteTask from '../screens/appflow/CompleteTasks/CompleteTasks';
import OngoingTask from '../screens/appflow/OngoingTasks/OngoingTasks';
import TaskDetailScreen from '../screens/appflow/TaskDetailScreen/TaskDetailScreen';
import {ITask} from '../interfaces/ITask';
import {IUser} from '../interfaces/IUser';
import PdfScreen from '../screens/appflow/PdfScreen/PdfScreen';

export type HomeStackParamsList = {
  BottomTabs: undefined;
  TaskDetails: {isCompleted: boolean; item: ITask; user?: IUser};
  Home: undefined;
  ProgressDetails: undefined;
  TeamsProgress: undefined;
  EditProfile: undefined;
  Notification: undefined;
  ChangePassword: undefined;
  UpdatedPassword: undefined;
  Policy: undefined;
  OngoingTask: undefined;
  CompleteTask: undefined;
  Addtask: undefined;
  Profile: undefined;
  PdfScreen: {attachmentUrl: string};
};

const Stack = createNativeStackNavigator<HomeStackParamsList>();

const HomeStackNavigation = () => {
  return (
    <Stack.Navigator
      screenOptions={({navigation}) => ({
        headerShown: true,
        headerTitleAlign: 'center',
        headerShadowVisible: false,
        headerTitleStyle: {
          color: Colors.primary,
          fontSize: 14,
          fontFamily: Fonts.Semibold,
        },
        headerStyle: {
          backgroundColor: Colors.background,
        },
        headerLeft: () => (
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Image
              style={{height: 22, width: 22, marginLeft: 8}}
              source={images.leftarrow}
              resizeMode="contain"
            />
          </TouchableOpacity>
        ),
        navigationBarColor: Colors.white,
      })}>
      <Stack.Screen
        name="BottomTabs"
        component={BottomTabs}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="Home"
        component={Home}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="TaskDetails"
        component={TaskDetailScreen}
        options={{headerShown: true, headerTitle: 'Task Details'}}
      />

      <Stack.Screen
        name="ProgressDetails"
        component={ProgressDetails}
        options={{headerShown: true, headerTitle: ''}}
      />

      <Stack.Screen
        name="EditProfile"
        component={EditProfile}
        options={{headerShown: true, headerTitle: ''}}
      />

      <Stack.Screen
        name="Notification"
        component={Notification}
        options={{
          headerShown: true,
          headerTitle: 'Notifications',
          headerTitleStyle: {
            color: Colors.primary,
            fontSize: 14,
            fontFamily: Fonts.Semibold,
          },
        }}
      />

      <Stack.Screen
        name="ChangePassword"
        component={ChangePassword}
        options={{headerShown: true, headerTitle: ''}}
      />

      <Stack.Screen
        name="UpdatedPassword"
        component={UpdatedPassword}
        options={{headerShown: false, headerTitle: ''}}
      />

      <Stack.Screen
        name="Policy"
        component={Policy}
        options={{headerShown: true, headerTitle: 'Privacy Policy'}}
      />

      <Stack.Screen
        name="OngoingTask"
        component={OngoingTask}
        options={{
          headerShown: true,
          headerTitle: 'Ongoing Tasks',
        }}
      />

      <Stack.Screen
        name="CompleteTask"
        component={CompleteTask}
        options={{
          headerShown: true,
          headerTitle: 'Complete Tasks',
        }}
      />

      <Stack.Screen
        name="PdfScreen"
        component={PdfScreen}
        options={{
          headerShown: true,
          headerTitle: '',
        }}
      />
    </Stack.Navigator>
  );
};

export default HomeStackNavigation;
