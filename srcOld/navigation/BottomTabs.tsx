import * as React from 'react';
import {Image, View, StyleSheet, TouchableOpacity} from 'react-native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import Home from '../screens/appflow/Home/Home';
import Calendar from '../screens/appflow/Calendar/Calendar';
import Profile from '../screens/appflow/Profile/Profile';
import {images} from '../assets/images';
import Addtask from '../screens/appflow/Addtask/Addtask';
import {Colors, Fonts} from '../utilities/theme/theme';
import TeamsProgress from '../screens/appflow/TeamsProgress/TeamsProgress';
import {AddTaskIcon, ArrowLeftIcon} from '../assets/svgIcons';
import {useNavigation} from '@react-navigation/native';
import {ITask} from '../interfaces/ITask';
import {s} from 'react-native-size-matters';

export type BottomTabParamlist = {
  Home: undefined;
  Calendar: undefined;
  TeamsProgress: undefined;
  Addtask: undefined;
  Profile: undefined;
  TaskDetails: {isCompleted: boolean; item?: ITask};
  ProgressDetails: undefined;
  EmployeesModal: undefined;
};

const Tab = createBottomTabNavigator<BottomTabParamlist>();

function BottomTabs() {
  const navigation = useNavigation();
  return (
    <Tab.Navigator
      initialRouteName="Home"
      screenOptions={{
        tabBarHideOnKeyboard: true,

        tabBarActiveTintColor: Colors.buttonbgcolor,
        tabBarStyle: {
          paddingTop: 20,
        },
        tabBarLabelStyle: {marginTop: 10},
        headerStyle: {
          backgroundColor: Colors.background,
        },
        headerTitleStyle: {
          color: Colors.primary,
          fontSize: 14,
          fontFamily: Fonts.Semibold,
        },
        headerTitleAlign: 'center',

        headerShown: true,
        headerLeft: () => (
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{marginLeft: 24}}>
            <ArrowLeftIcon />
          </TouchableOpacity>
        ),
      }}>
      <Tab.Screen
        name="Home"
        component={Home}
        options={{
          headerTitle: 'Home',
          headerTitleAlign: 'center',
          headerShadowVisible: false,
          headerTitleStyle: {
            color: Colors.primary,
            fontSize: 14,
            fontFamily: Fonts.Semibold,
          },
          tabBarLabel: 'Home',
          headerShown: false,
          tabBarIcon: ({focused}) => (
            <Image
              style={[
                styles.icon,
                {tintColor: focused ? Colors.buttonbgcolor : Colors.tintColor},
              ]}
              source={images.homeicon}
              resizeMode="contain"
            />
          ),
        }}
      />

      <Tab.Screen
        name="Calendar"
        component={Calendar}
        options={({navigation}) => ({
          headerShown: true,
          headerTitle: 'Schedule',
          headerTitleAlign: 'center',
          headerShadowVisible: false,
          tabBarLabel: 'Calendar',
          headerTitleStyle: {
            color: Colors.primary,
            fontSize: 14,
            fontFamily: Fonts.Semibold,
          },

          tabBarIcon: ({focused}) => (
            <Image
              style={[
                styles.icon,
                {tintColor: focused ? Colors.buttonbgcolor : Colors.tintColor},
              ]}
              source={images.calendaricon}
              resizeMode="contain"
            />
          ),
          headerRight: () => (
            <TouchableOpacity
              style={{marginRight: s(24)}}
              onPress={() => navigation.navigate('Addtask')}>
              <AddTaskIcon />
            </TouchableOpacity>
          ),
        })}
      />

      <Tab.Screen
        name="Addtask"
        component={Addtask}
        options={{
          headerShown: true,
          headerShadowVisible: false,
          headerTitle: 'Create New Task',
          tabBarLabel: '',
          headerTitleStyle: {
            color: Colors.primary,
            fontSize: 14,
            fontFamily: Fonts.Semibold,
          },
          tabBarIcon: ({focused}) => (
            <View style={styles.AddtaskStyle}>
              <Image
                style={[
                  styles.icon,
                  {tintColor: focused ? Colors.white : Colors.white},
                ]}
                source={images.addtaskicon}
                resizeMode="contain"
              />
            </View>
          ),
        }}
      />

      <Tab.Screen
        name="TeamsProgress"
        component={TeamsProgress}
        options={{
          headerShown: true,
          headerTitle: '',

          tabBarIcon: ({focused}) => (
            <Image
              style={[
                styles.icon,
                {tintColor: focused ? Colors.buttonbgcolor : Colors.tintColor},
              ]}
              source={images.teamprogressicon}
              resizeMode="contain"
            />
          ),
        }}
      />

      <Tab.Screen
        name="Profile"
        component={Profile}
        options={{
          headerShadowVisible: false,
          tabBarLabel: 'Profile',
          headerTitle: '',
          tabBarIcon: ({focused}) => (
            <Image
              style={[
                styles.icon,
                {tintColor: focused ? Colors.buttonbgcolor : Colors.tintColor},
              ]}
              source={images.profileicon}
              resizeMode="contain"
            />
          ),
        }}
      />
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({
  icon: {
    width: 20,
    height: 20,
  },
  AddtaskStyle: {
    width: 46,
    height: 46,
    backgroundColor: Colors.buttonbgcolor,
    alignItems: 'center',
    justifyContent: 'center',

    marginTop: 12,
  },
});

export default BottomTabs;
