import * as React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import Signin from '../screens/Authflow/Signin/Signin';
import Signup from '../screens/Authflow/Signup/Signup';
import Forgotpassword from '../screens/Authflow/Forgotpassword/Forgotpassword';
import {TouchableOpacity, Image} from 'react-native';
import {images} from '../assets/images';
import {Colors} from '../utilities/theme/theme';
import VerificationCode from '../screens/Authflow/VerificationCode/VerificationCode';
import CreatePassword from '../screens/Authflow/CreatePassword/CreatePassword';
import Splash from '../screens/Authflow/SplashScreen/SplashScreen';
import PasswordRecovered from '../screens/Authflow/PasswordRecovered/PasswordRecovered';
import OnBoarding from '../screens/Authflow/OnBoarding/OnBoarding';
import {AuthContext} from '../../App';

export type AuthStackParamsList = {
  Signin: undefined;
  Signup: undefined;
  Forgotpassword: undefined;
  VerificationCode: undefined;
  CreatePassword: undefined;
  RecoverPassword: undefined;
  Splash: undefined;
  OnBoarding: undefined;
};

const Stack = createNativeStackNavigator<AuthStackParamsList>();

const AuthStackNavigation = () => {
  const {firstLaunch} = React.useContext(AuthContext);
  return (
    <Stack.Navigator
      screenOptions={({navigation}) => ({
        headerShown: true,
        headerTitleAlign: 'center',
        headerShadowVisible: false,
        headerTitleStyle: {
          color: '#000000',
          fontSize: 17,
        },
        headerStyle: {
          backgroundColor: Colors.background,
        },
        headerLeft: () => (
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Image
              style={{height: 22, width: 22}}
              source={images.leftarrow}
              resizeMode="contain"
            />
          </TouchableOpacity>
        ),
        navigationBarColor: Colors.white,
      })}>
      {firstLaunch && (
        <Stack.Screen
          name="OnBoarding"
          component={OnBoarding}
          options={{headerShown: false}}
        />
      )}
      <Stack.Screen
        name="Splash"
        component={Splash}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="Signin"
        component={Signin}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="Signup"
        component={Signup}
        options={{headerShown: false}}
      />

      <Stack.Screen
        name="Forgotpassword"
        component={Forgotpassword}
        options={{headerShown: true, headerTitle: ''}}
      />

      <Stack.Screen
        name="VerificationCode"
        component={VerificationCode}
        options={{headerShown: true, headerTitle: ''}}
      />

      <Stack.Screen
        name="CreatePassword"
        component={CreatePassword}
        options={{headerShown: true, headerTitle: ''}}
      />

      <Stack.Screen
        name="RecoverPassword"
        component={PasswordRecovered}
        options={{headerShown: false}}
      />
    </Stack.Navigator>
  );
};

export default AuthStackNavigation;
