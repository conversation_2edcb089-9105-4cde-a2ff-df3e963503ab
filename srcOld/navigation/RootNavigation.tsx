import React, {useContext, useEffect, useState} from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {AuthContext} from '../../App';
import HomeStackNavigation from './HomeStackNavigation';
import AuthStackNavigation from './AuthStackNavigation';
import {NotificationHandlerHOC} from '../helpers/pushNotification';
import messaging from '@react-native-firebase/messaging';
import Modal from 'react-native-modal';
import {Image, StyleSheet, Text, View} from 'react-native';
import {Colors, Fonts} from '../utilities/theme/theme';
import {verticalScale} from 'react-native-size-matters';
import {images} from '../assets/images';

interface INotificationProps {
  title: string;
  description: string;
}
const RootNavigation = () => {
  const {userId} = useContext(AuthContext);
  const [isModalVisible, setModalVisible] = useState(false);
  const [notificationData, setNotificationData] =
    useState<INotificationProps>();

  const WrappedRoot = NotificationHandlerHOC(
    userId ? HomeStackNavigation : AuthStackNavigation,
  );
  const handleModalBackdrop = () => {
    setModalVisible(false);
    setNotificationData(undefined);
  };

  useEffect(() => {
    messaging().onMessage(async remoteMessage => {
      setModalVisible(true);
      setNotificationData({
        title: remoteMessage?.notification?.body || '',
        description: remoteMessage.notification?.title || '',
      });
    });
  }, []);
  useEffect(() => {
    setTimeout(() => {
      setModalVisible(false);
      setNotificationData(undefined);
    }, 2000);
  }, [isModalVisible]);

  return (
    <NavigationContainer>
      <WrappedRoot />
      <Modal isVisible={isModalVisible} onBackdropPress={handleModalBackdrop}>
        <View style={styles.notificationContainer}>
          <Image
            source={images.logo}
            style={styles.logo}
            resizeMode="contain"
          />
          <View style={{marginLeft: 10}}>
            <Text style={styles.title}>{notificationData?.description}</Text>
            <Text style={styles.description} numberOfLines={1}>
              {notificationData?.title}
            </Text>
          </View>
        </View>
      </Modal>
    </NavigationContainer>
  );
};
export default NotificationHandlerHOC(RootNavigation);
const styles = StyleSheet.create({
  notificationContainer: {
    backgroundColor: Colors.white,
    borderRadius: 10,
    alignItems: 'center',
    paddingVertical: verticalScale(8),
    paddingHorizontal: 8,
    flexDirection: 'row',
    position: 'absolute',
    top: 40,
    width: '100%',
    alignSelf: 'center',
  },
  description: {
    fontFamily: Fonts.Medium,
    fontSize: 12,
    color: Colors.placeholder,
    marginTop: 5,
    width: '90%',
  },
  title: {fontFamily: Fonts.Semibold, fontSize: 15},
  logo: {height: 40, width: 40},
});
