import React, {useContext} from 'react';
import {
  Text,
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import {images} from '../../../assets/images';
import Input from '../../../component/Input/Input';
import {Button} from '../../../component/Button/Button';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {useState} from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamsList} from '../../../navigation/AuthStackNavigation';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {AuthContext} from '../../../../App';
import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import {IUser} from '../../../interfaces/IUser';
import Toast from 'react-native-toast-message';

type Props = NativeStackScreenProps<AuthStackParamsList, 'Signin'>;

const Signin: React.FC<Props> = ({navigation}) => {
  const {setUserId, setUserData} = useContext(AuthContext);
  const [hidePasswod, setHidePassword] = useState(true);
  const togglePassword = () => setHidePassword(!hidePasswod);
  const [isloading, setisloading] = useState(false);

  const fetchUser = async (useId: string) => {
    return await firestore().collection('Users').doc(useId).get();
  };

  const signInUser = async () => {
    try {
      setisloading(true);
      const res = await auth().signInWithEmailAndPassword(
        formik.values.email,
        formik.values.password,
      );
      await AsyncStorage.setItem('userId', res.user.uid);
      const userData = await fetchUser(res.user.uid);
      if (userData.exists) {
        setUserData(userData?.data() as IUser);
        setUserId(res.user.uid);
        const fcmToken = await AsyncStorage.getItem('fcmToken');
        await firestore()
          .collection('Users')
          .doc(res.user.uid)
          .update({fcmToken: fcmToken});
      }
    } catch (error: any) {
      if (error.code === 'auth/email-already-in-use') {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Password is incorrect!',
        });
      } else if (error.code === 'auth/invalid-email') {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'That email address is invalid!',
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Something went wrong. try again later',
        });
      }
      console.log('Error signing in ', error);
    } finally {
      setisloading(false);
    }
  };

  const validationSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email').required('Email is required'),
    password: Yup.string()
      .required('Password is required')
      .min(8, 'Password must be at least 8 characters'),
  });
  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
    },

    validationSchema: validationSchema,
    validateOnMount: true,
    onSubmit: signInUser,
  });

  return (
    <View style={styles.container}>
      <SafeAreaView />
      {/* {-----LOGO ------} */}
      <Image
        style={styles.logoStyle}
        source={images.logo}
        resizeMode="contain"
      />
      <Text style={styles.titleStyle}>Welcome Back!</Text>

      {/* {-----Email Container ------} */}
      <Input
        placeholder={'Enter your email'}
        keyboardType={'email-address'}
        leftIcon={images.emailicon}
        title={'Email Address'}
        containerStyle={{marginTop: 20}}
        onChangeText={formik.handleChange('email')}
        value={formik.values.email}
        onBlur={formik.handleBlur('email')}
        leftIconContainer={{height: 16, width: 16}}
      />
      {formik.touched.email && formik.errors.email ? (
        <Text style={styles.errorText}>{formik.errors.email}</Text>
      ) : null}

      {/* {-----Password Container ------} */}
      <Input
        placeholder={'Password'}
        title={'Password'}
        leftIcon={images.lockicon}
        isPassword={true}
        secureTextEntry={hidePasswod}
        onRightIconPress={togglePassword}
        containerStyle={{marginTop: 27}}
        onChangeText={formik.handleChange('password')}
        onFocus={formik.handleBlur('password')}
        value={formik.values.password}
        onBlur={formik.handleBlur('password')}
      />
      {formik.touched.password && formik.errors.password ? (
        <Text style={styles.errorText}>{formik.errors.password}</Text>
      ) : null}
      <TouchableOpacity onPress={() => navigation.navigate('Forgotpassword')}>
        <Text style={styles.textforget}>Forgot Password?</Text>
      </TouchableOpacity>

      {/* {-----Button Container ------} */}
      <View style={{marginTop: 40}}>
        <Button
          title={'Log In'}
          onPress={formik.handleSubmit}
          isLoading={isloading}
          disabled={isloading || !formik.isValid}
        />
      </View>
      <View style={styles.bottomline}>
        <Text style={styles.bottomText}>Don’t have an account?</Text>
        <TouchableOpacity onPress={() => navigation.navigate('Signup')}>
          <Text style={styles.signupText}> Sign Up</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    paddingHorizontal: 24,
    backgroundColor: Colors.background,
    paddingTop: 44,
    paddingBottom: 45,
  },
  titleStyle: {
    color: Colors.primary,
    fontSize: 20,
    fontFamily: Fonts.Semibold,
    paddingTop: 30,
  },
  logoStyle: {
    width: 160,
    height: 52,
    alignSelf: 'center',
  },
  textforget: {
    color: Colors.primary,
    fontSize: 10,
    fontFamily: Fonts.Medium,
    textAlign: 'right',
    marginTop: 10,
  },
  bottomline: {
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: 30,
    marginBottom: '100%',
  },
  bottomText: {
    color: Colors.lightcolor,
    fontSize: 14,
    fontFamily: Fonts.Medium,
  },
  signupText: {
    color: Colors.buttonbgcolor,
    fontSize: 14,
    fontFamily: Fonts.Medium,
  },
  errorText: {
    color: Colors.red,
    fontSize: 12,
    marginTop: 5,
  },
});

export default Signin;
