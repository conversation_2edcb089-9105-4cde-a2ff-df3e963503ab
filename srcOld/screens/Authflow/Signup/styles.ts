import {StyleSheet} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: Colors.background,
    paddingTop: 44,
  },
  titleStyle: {
    color: Colors.primary,
    fontSize: 20,
    fontFamily: Fonts.Semibold,
    paddingTop: 30,
  },
  logoStyle: {
    width: 160,
    height: 52,
    alignSelf: 'center',
  },
  bottomLine: {
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: 30,
    marginBottom: 40,
  },
  bottomText: {
    color: Colors.lightcolor,
    fontSize: 14,
    fontFamily: Fonts.Medium,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 17,
  },
  textContainer: {
    paddingRight: 40,
    paddingLeft: 10,
  },
  configureText: {
    color: Colors.primary,
    fontSize: 12,
    fontFamily: Fonts.Regular,
  },
  policyText: {
    color: Colors.buttonbgcolor,
    fontSize: 12,
    fontFamily: Fonts.Regular,
  },
  loginText: {
    color: Colors.buttonbgcolor,
    fontSize: 14,
    fontFamily: Fonts.Medium,
  },
  errorText: {
    color: Colors.red,
    fontSize: 12,
    marginTop: 5,
  },
  checkBox: {
    width: 20,
    height: 20,
    marginTop: 2,
  },
  margin: {
    marginTop: 22,
  },
  privacyText: {
    fontSize: 12,
    fontFamily: Fonts.Regular,
    color: Colors.buttonbgcolor,
  },
});
