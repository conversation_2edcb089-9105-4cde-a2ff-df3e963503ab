import React, {useContext, useState} from 'react';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import {images} from '../../../assets/images';
import Input from '../../../component/Input/Input';
import {Button} from '../../../component/Button/Button';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {AuthContext} from '../../../../App';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamsList} from '../../../navigation/AuthStackNavigation';
import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from '@react-native-firebase/auth';
import firestore from '@react-native-firebase/firestore';
import {IUser} from '../../../interfaces/IUser';
import Toast from 'react-native-toast-message';
import {styles} from './styles';

type Props = NativeStackScreenProps<AuthStackParamsList, 'Signup'>;

const Signup: React.FC<Props> = ({navigation}) => {
  const [hidePassword, setHidePassword] = useState(true);
  const togglePassword = () => setHidePassword(!hidePassword);
  const {setUserId, setUserData} = useContext(AuthContext);
  const [isSelected, setSelected] = useState(false);
  const [isloading, setisloading] = useState(false);

  const toggleSelect = () => {
    setSelected(!isSelected);
  };

  const handleSignup = async () => {
    setisloading(true);

    await auth()
      .createUserWithEmailAndPassword(
        formik.values.email,
        formik.values.password,
      )
      .then(async res => {
        const fcmToken = await AsyncStorage.getItem('fcmToken');

        const payload: IUser = {
          name: formik.values.name,
          email: formik.values.email,
          userType: 'user',
          id: res.user.uid,
          progress: 0,
          fcmToken: fcmToken ? fcmToken : '',
        };
        AsyncStorage.setItem('userId', res.user.uid);
        setUserId(res.user.uid);
        await storeUserData(res.user.uid);
        setUserData(payload);
      })
      .catch(error => {
        if (error.code === 'auth/email-already-in-use') {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'That email address is already in use!',
          });
        } else if (error.code === 'auth/invalid-email') {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'That email address is invalid!',
          });
        } else {
          Toast.show({
            type: 'error',
            text1: 'Error',
            text2: 'Error signing up. Try again later',
          });
        }
        console.log('Error signing up ', error);
      })
      .finally(() => {
        setisloading(false);
      });
  };

  const storeUserData = async (userId: string) => {
    const fcmToken = await AsyncStorage.getItem('fcmToken');

    const payload = {
      name: formik.values.name,
      email: formik.values.email,
      userType: 'user',
      fcmToken: fcmToken,
    };
    firestore()
      .collection('Users')
      .doc(userId)
      .set(payload)
      .then(() => {
        console.log('User added!');
      })
      .catch(error => {
        console.error('Error adding user: ', error);
        Toast.show({
          type: 'error',
          text1: 'Error',
          text2: 'Error saving data',
        });
      });
  };

  const validationSchema = Yup.object().shape({
    name: Yup.string().required('Name required'),
    email: Yup.string().email('Invalid email').required('Email is required'),
    password: Yup.string()
      .required('Password is required')
      .min(8, 'Password must be at least 8 characters'),
  });

  const formik = useFormik({
    initialValues: {
      name: '',
      email: '',
      password: '',
    },
    validationSchema: validationSchema,
    validateOnMount: true,
    onSubmit: handleSignup,
  });

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <SafeAreaView />
        <Image
          style={styles.logoStyle}
          source={images.logo}
          resizeMode="contain"
        />
        <Text style={styles.titleStyle}>Create your account</Text>

        <Input
          placeholder={'Full Name'}
          leftIcon={images.usericon}
          title={'Full Name'}
          containerStyle={styles.margin}
          onChangeText={formik.handleChange('name')}
          value={formik.values.name}
          onBlur={formik.handleBlur('name')}
        />
        {formik.touched.name && formik.errors.name ? (
          <Text style={styles.errorText}>{formik.errors.name}</Text>
        ) : null}

        <Input
          placeholder={'Enter your email'}
          keyboardType={'email-address'}
          leftIcon={images.emailicon}
          title={'Email Address'}
          containerStyle={styles.margin}
          onChangeText={formik.handleChange('email')}
          value={formik.values.email}
          onBlur={formik.handleBlur('email')}
        />
        {formik.touched.email && formik.errors.email ? (
          <Text style={styles.errorText}>{formik.errors.email}</Text>
        ) : null}

        <Input
          placeholder={'Password'}
          title={'Password'}
          leftIcon={images.lockicon}
          isPassword={true}
          secureTextEntry={hidePassword}
          onRightIconPress={togglePassword}
          containerStyle={styles.margin}
          onChangeText={formik.handleChange('password')}
          value={formik.values.password}
          onBlur={formik.handleBlur('password')}
          onFocus={formik.handleBlur('password')}
        />
        {formik.touched.password && formik.errors.password ? (
          <Text style={styles.errorText}>{formik.errors.password}</Text>
        ) : null}

        <View style={styles.checkboxContainer}>
          <TouchableOpacity onPress={toggleSelect}>
            <Image
              style={styles.checkBox}
              source={isSelected ? images.checkedImage : images.uncheckedImage}
            />
          </TouchableOpacity>
          <View style={styles.textContainer}>
            <Text style={styles.configureText}>
              I have read & agreed to
              <Text style={styles.privacyText}> Privacy Policy,</Text>
            </Text>
            <Text style={styles.policyText}>Terms & Condition</Text>
          </View>
        </View>

        <Button
          title={'Sign Up'}
          onPress={formik.handleSubmit}
          isLoading={isloading}
          disabled={isloading || !formik.isValid || !isSelected}
          containerStyle={{marginTop: 29}}
        />
        <View style={styles.bottomLine}>
          <Text style={styles.bottomText}>Already have an account?</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Signin')}>
            <Text style={styles.loginText}> Log In</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

export default Signup;
