import React from 'react';
import {Text, StyleSheet, View, ScrollView} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import Input from '../../../component/Input/Input';
import {images} from '../../../assets/images';
import {useState} from 'react';
import {Button} from '../../../component/Button/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamsList} from '../../../navigation/AuthStackNavigation';
import {useFormik} from 'formik';
import * as Yup from 'yup';

type Props = NativeStackScreenProps<AuthStackParamsList, 'CreatePassword'>;

const CreatePassword: React.FC<Props> = ({navigation}) => {
  const [hidePassword, setHidePassword] = useState(true);
  const [cnfHidePassword, setCnfHidePassword] = useState(true);

  const togglePassword = () => setHidePassword(!hidePassword);
  const toggleCnfPassword = () => setCnfHidePassword(!cnfHidePassword);

  const validationSchema = Yup.object().shape({
    password: Yup.string()
      .required('Password is required')
      .min(8, 'Password must be at least 8 characters'),
    cnfpassword: Yup.string()
      .required('Confirm Password required')
      .oneOf([Yup.ref('password'), 'null'], 'Passwords must match'),
  });

  const formik = useFormik({
    initialValues: {
      password: '',
      cnfpassword: '',
    },
    validationSchema: validationSchema,
    onSubmit: values => {
      navigation.navigate('RecoverPassword');
    },
  });

  return (
    <ScrollView>
      <View style={styles.container}>
        <Text style={styles.mainTitle}>Create New Password</Text>
        <Text style={styles.subTitle}>
          Create a new password that is safer and easier to remember.
        </Text>

        <Input
          title="Password"
          placeholder="Enter New Password"
          leftIcon={images.lockicon}
          isPassword={true}
          secureTextEntry={hidePassword}
          onRightIconPress={togglePassword}
          containerStyle={{marginTop: 38}}
          onChangeText={formik.handleChange('password')}
          value={formik.values.password}
          onBlur={formik.handleBlur('password')}
          onLeftIconPress={togglePassword}
        />
        {formik.touched.password && formik.errors.password ? (
          <Text style={styles.errorText}>{formik.errors.password}</Text>
        ) : null}

        <Input
          title="Confirm Password"
          placeholder="Confirm Password"
          leftIcon={images.lockicon}
          isPassword={true}
          secureTextEntry={cnfHidePassword}
          onRightIconPress={toggleCnfPassword}
          containerStyle={{marginTop: 14}}
          onChangeText={formik.handleChange('cnfpassword')}
          value={formik.values.cnfpassword}
          onBlur={formik.handleBlur('cnfpassword')}
          onLeftIconPress={toggleCnfPassword}
        />
        {formik.touched.cnfpassword && formik.errors.cnfpassword ? (
          <Text style={styles.errorText}>{formik.errors.cnfpassword}</Text>
        ) : null}

        <View style={{marginTop: 59}}>
          <Button title="Recover Password" onPress={formik.handleSubmit} />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: Colors.background,
  },
  mainTitle: {
    fontSize: 20,
    fontFamily: Fonts.Bold,
    color: Colors.primary,
    paddingTop: 26,
  },
  subTitle: {
    fontSize: 14,
    fontFamily: Fonts.Medium,
    color: Colors.lightcolor,
    lineHeight: 21,
    paddingTop: 18,
  },
  errorText: {
    color: Colors.red,
    fontSize: 12,
    marginTop: 5,
  },
});

export default CreatePassword;
