import React, {useContext, useEffect, useState} from 'react';
import {
  Text,
  StyleSheet,
  View,
  SectionList,
  ActivityIndicator,
} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import NotificationComp from '../../../component/NotificationComp/NotificationComp';
import firestore from '@react-native-firebase/firestore';
import {AuthContext} from '../../../../App';
import {INotification} from '../../../interfaces/INotification';
import {images} from '../../../assets/images';
interface NotificationSections {
  title: string;
  data: INotification[];
}

const Notification = () => {
  const [notificationsData, setNotificationsData] = useState<INotification[]>(
    [],
  );
  const [isLoading, setIsLoading] = useState(true);
  const {userId} = useContext(AuthContext);

  const fetchNotifications = async () => {
    try {
      setIsLoading(true);
      await firestore()
        .collection('Notifications')
        .where('sentTo', 'array-contains', userId)
        .get()
        .then(querySnapshot => {
          const data = querySnapshot.docs.map(doc => ({
            ...doc.data(),
            id: doc.id,
          })) as INotification[];
          setNotificationsData(data);
          updateNotifications(data);
        });
    } catch (error) {
      console.log('Error fetching notifications', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateNotifications = async (notifications: INotification[]) => {
    try {
      const batch = firestore().batch();
      notifications.forEach(notification => {
        const docRef = firestore()
          .collection('Notifications')
          .doc(notification.id);
        batch.update(docRef, {isRead: true});
      });

      await batch.commit();
    } catch (error) {
      console.log('Error updating notifications', error);
    }
  };
  useEffect(() => {
    fetchNotifications();
  }, []);

  const groupedNotifications = notificationsData.reduce(
    (groups: Record<string, INotification[]>, notification) => {
      const key = notification.isRead ? 'Earlier' : 'New';
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(notification);
      return groups;
    },
    {New: [], Earlier: []} as Record<string, INotification[]>,
  );

  const sections: NotificationSections[] = [
    ...(groupedNotifications.New.length > 0
      ? [{title: 'New', data: groupedNotifications.New}]
      : []),

    {title: 'Earlier', data: groupedNotifications?.Earlier},
  ];

  return (
    <View style={styles.container}>
      {isLoading ? (
        <ActivityIndicator
          size={'large'}
          color={Colors.buttonbgcolor}
          style={styles.activityIndicator}
        />
      ) : !notificationsData.length ? (
        <Text style={styles.emptyText}>No notifications received yet</Text>
      ) : (
        <SectionList
          sections={sections}
          renderItem={({item}) => (
            <NotificationComp
              title={item.message?.notification.title}
              body={item.message.notification.body}
              lefticon={images.avatarPlaceholder}
              time={item.createdAt}
            />
          )}
          renderSectionHeader={({section: {title}}) => (
            <Text style={styles.sectionHeader}>{title}</Text>
          )}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: 24,
  },

  sectionHeader: {
    fontSize: 18,
    fontFamily: Fonts.Semibold,
    color: Colors.primary,
    marginTop: 30,
  },
  emptyText: {
    fontFamily: Fonts.Medium,
    fontSize: 12,
    textAlign: 'center',
    color: Colors.primary,
    marginTop: 100,
  },
  activityIndicator: {alignSelf: 'center', marginTop: 100},
});

export default Notification;
