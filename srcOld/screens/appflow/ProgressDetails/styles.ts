import {StyleSheet} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {ms, s, vs} from 'react-native-size-matters';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  circularProgressContainer: {
    alignSelf: 'center',
    marginTop: vs(15),
  },
  avatarLarge: {
    height: ms(82),
    width: ms(82),
    borderRadius: 100,
  },
  userInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: vs(4),
    alignSelf: 'center',
    marginTop: vs(10),
  },
  userName: {
    color: Colors.buttonbgcolor,
    fontFamily: Fonts.Semibold,
    fontSize: ms(12),
  },
  taskInfo: {
    fontSize: ms(10),
    fontFamily: Fonts.Semibold,
    color: '#A3A2A2',
    textAlign: 'center',
    marginTop: vs(3),
  },
  taskInfoHighlight: {
    color: '#828282',
  },
  progressPercentage: {
    fontFamily: Fonts.Bold,
    fontSize: ms(16),
    color: Colors.primary,
    textAlign: 'center',
    marginTop: vs(3),
  },
  progressPercentageSymbol: {
    fontSize: ms(10),
    color: '#A3A2A2',
    fontFamily: Fonts.Semibold,
  },
  sectionHeader: {
    marginTop: vs(18),
    fontFamily: Fonts.Semibold,
    fontSize: ms(12),
    lineHeight: 18,
    color: Colors.primary,
    paddingHorizontal: s(22),
  },
  emptyText: {
    fontFamily: Fonts.Semibold,
    color: Colors.placeholder,
    fontSize: ms(12),
    textAlign: 'center',
    marginTop: 20,
  },
  rightActionContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: ms(60),
    backgroundColor: '#55AB67',
    marginTop: 12,
    marginLeft: -20,
  },
  doneText: {
    color: 'green',
    fontSize: ms(16),
    fontWeight: 'bold',
    alignSelf: 'center',
  },
  emptyContainer: {alignItems: 'center', marginTop: 40},
});
