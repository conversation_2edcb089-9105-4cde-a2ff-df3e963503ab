import React, {FC, useContext, useEffect, useRef} from 'react';
import {
  Text,
  View,
  Image,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import {Colors} from '../../../utilities/theme/theme';
import {ms} from 'react-native-size-matters';
import {CircularProgressBase} from 'react-native-circular-progress-indicator';
import {images} from '../../../assets/images';
import AssignedTaskComponent from '../../../component/AssignedTaskComponent/AssignedTaskComponent';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {AuthContext} from '../../../../App';
import {styles} from './styles';
import {ITask} from '../../../interfaces/ITask';
import moment from 'moment';
import {Swipeable, GestureHandlerRootView} from 'react-native-gesture-handler';
import {MarkDone, OngoingEmptyState} from '../../../assets/svgIcons';
import useTasks from '../../../hooks/useTasks';

type Props = NativeStackScreenProps<HomeStackParamsList, 'ProgressDetails'>;

const ProgressDetails: FC<Props> = ({navigation, route}) => {
  const circularProgressRef = useRef(null);
  const {userData} = route.params;

  const {fetchTasks, fetchTasksById, isLoading, tasks, markTaskAsDone} =
    useTasks();

  useEffect(() => {
    if (route?.params?.isMember) {
      fetchTasksById(userData?.id);
    } else {
      fetchTasks();
    }
  }, []);

  const today = moment().startOf('day');
  const todaysTasks = tasks.filter(task =>
    moment(task.date.toDate()).isSame(today, 'day'),
  );
  const pendingTasks = tasks.filter(task => task.status === 'pending');
  const pastTasks = tasks.filter(task =>
    moment(task.date.toDate()).isBefore(today, 'day'),
  );

  const renderSwipeableTask = (item: ITask) => (
    <Swipeable
      key={item.id}
      renderRightActions={() => (
        <TouchableOpacity
          style={styles.rightActionContainer}
          onPress={() => markTaskAsDone(item.id)}>
          <MarkDone />
        </TouchableOpacity>
      )}
      childrenContainerStyle={{alignSelf: 'center'}}>
      <AssignedTaskComponent
        onPress={() =>
          navigation.navigate('TaskDetails', {isCompleted: false, item})
        }
        item={item}
        pending={item.status === 'done'}
        delayed={
          moment(item.date.toDate()).isBefore(new Date()) &&
          item.status !== 'done'
        }
        containerStyle={{width: '88%'}}
      />
    </Swipeable>
  );

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <View style={styles.container}>
        <ScrollView showsVerticalScrollIndicator={false} style={{flexGrow: 1}}>
          <View style={{paddingBottom: 40}}>
            {isLoading ? (
              <ActivityIndicator
                size="large"
                style={{marginTop: 100}}
                color={Colors.buttonbgcolor}
              />
            ) : (
              <>
                <View style={styles.circularProgressContainer}>
                  <CircularProgressBase
                    initialValue={1}
                    ref={circularProgressRef}
                    activeStrokeWidth={4}
                    inActiveStrokeWidth={4}
                    inActiveStrokeOpacity={0.2}
                    value={parseInt(userData.progress?.toFixed(0) || '0')}
                    radius={ms(50)}
                    activeStrokeColor={Colors.buttonbgcolor}
                    inActiveStrokeColor="#CDCDCD"
                    strokeLinecap="round"
                    maxValue={100}>
                    <Image
                      source={
                        userData.profileImage?.url
                          ? {uri: userData.profileImage.url}
                          : images.avatarPlaceholder
                      }
                      style={styles.avatarLarge}
                    />
                  </CircularProgressBase>
                </View>
                <View style={styles.userInfoContainer}>
                  <Text style={styles.userName}>{userData.name}</Text>
                </View>
                <Text style={styles.taskInfo}>
                  Completed{' '}
                  <Text style={styles.taskInfoHighlight}>
                    {tasks.filter(task => task.status === 'done').length}
                  </Text>{' '}
                  pending{' '}
                  <Text style={styles.taskInfoHighlight}>
                    {pendingTasks.length}
                  </Text>{' '}
                </Text>
                <Text style={styles.progressPercentage}>
                  {userData.progress?.toFixed(0) || 0}
                  <Text style={styles.progressPercentageSymbol}>%</Text>
                </Text>
                {!tasks.length ? (
                  <View style={styles.emptyContainer}>
                    <OngoingEmptyState height={130} />
                    <Text style={styles.emptyText}>No tasks added yet</Text>
                  </View>
                ) : (
                  <>
                    {todaysTasks.length > 0 && (
                      <>
                        <Text style={styles.sectionHeader}>Today’s Task</Text>
                        {todaysTasks.map(item => renderSwipeableTask(item))}
                      </>
                    )}
                    {pendingTasks.length > 0 && (
                      <>
                        <Text style={styles.sectionHeader}>Pending</Text>
                        {pendingTasks.map(item => renderSwipeableTask(item))}
                      </>
                    )}
                    {pastTasks.length > 0 && (
                      <>
                        <Text style={[styles.sectionHeader, {marginTop: 15}]}>
                          Past Date
                        </Text>
                        {pastTasks.map(item => renderSwipeableTask(item))}
                      </>
                    )}
                  </>
                )}
              </>
            )}
          </View>
        </ScrollView>
      </View>
    </GestureHandlerRootView>
  );
};

export default ProgressDetails;
