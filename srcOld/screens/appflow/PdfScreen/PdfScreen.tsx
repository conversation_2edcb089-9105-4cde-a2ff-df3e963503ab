import React from 'react';
import {StyleSheet, View} from 'react-native';
import {Colors} from '../../../utilities/theme/theme';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import Pdf from 'react-native-pdf';

type Props = NativeStackScreenProps<HomeStackParamsList, 'PdfScreen'>;

const PdfScreen: React.FC<Props> = ({route}) => {
  const {attachmentUrl} = route.params;

  return (
    <View style={styles.container}>
      <Pdf
        trustAllCerts={true}
        source={{
          uri: attachmentUrl,
          cache: true,
        }}
        onLoadComplete={(numberOfPages, filePath) => {
          console.log(`Number of pages: ${numberOfPages}`);
        }}
        onPageChanged={(page, numberOfPages) => {
          console.log(`Current page: ${page}`);
        }}
        onError={error => {
          console.error('PDF loading error:', error);
        }}
        style={styles.pdf}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: 18,
  },
  pdf: {
    flex: 1,
    width: '100%',
    height: '100%',
    marginTop: 15,
  },
});

export default PdfScreen;
