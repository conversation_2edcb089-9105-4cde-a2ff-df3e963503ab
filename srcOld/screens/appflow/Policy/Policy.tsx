import React, {useState} from 'react';
import {Text, StyleSheet, View, Image, TouchableOpacity} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {images} from '../../../assets/images';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';

type Props = NativeStackScreenProps<HomeStackParamsList, 'Policy'>;

const Policy: React.FC<Props> = () => {
  const [isSelected, setSelected] = useState(false);

  const toggleSelect = () => {
    setSelected(!isSelected);
  };

  return (
    <View style={styles.container}>
      <Image style={styles.imgStyle} source={images.logo} />
      <View style={styles.TitleContainer}>
        <Text style={styles.titleStyle}>Akhdar</Text>
      </View>
      <View style={styles.subtitleContainer}>
        <Text style={styles.policyText1}>
          Lorem ipsum dolor sit amet, consecteturt adipiscing elit. Vel maecenas
          sed nfjyunc nullam duis. Dignissim leo dictum massa sit sit. Nisl
          volutpat, magna faucibus odio risus. In ut odio lorem quam interdum.
          Sit Lorem ipsum dolor sit amet, consecteturt adipiscing elit. Vel
          maecenas sed nuncer nullam duis.
        </Text>
        <Text style={styles.policyText1}>
          Dignissim leo dictum massa sit sit. Nisl volutpat, magna faucibus odio
          risus. In ut odio lorem interdum. Sitmagna vel pellentesque risus
          amet, tic mattis mi, lacus. Eu senectus maecenass condimentum nibh
          morbi. Suspendessec tristique vestibulum lectus suspendisseer
        </Text>
        <Text style={styles.policyText1}>
          Sitmagna vel pellentesque risus amet, tic mattis mi, lacus. Eu
          senectus maecenass condimentum nibh morbi. Suspendessec tristique
          vestibulum lectus suspendisseer facilisi vestibulum , in.
        </Text>

        <View style={styles.bottomline}>
          <TouchableOpacity onPress={toggleSelect}>
            <Image
              style={styles.checkBox}
              source={isSelected ? images.checkedImage : images.uncheckedImage}
            />
          </TouchableOpacity>
          <Text style={styles.bottomline1}>I agree to </Text>
          <Text style={styles.bottomline2}>Privacy Policy</Text>
          <Text style={styles.bottomline1}>of Open Car.</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: 20,
  },
  imgStyle: {
    height: 52,
    width: 160,
    alignSelf: 'center',
    marginTop: 30,
  },
  TitleContainer: {
    backgroundColor: Colors.buttonbgcolor,
    height: 64,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    marginTop: 40,
  },
  titleStyle: {
    fontSize: 26,
    fontFamily: Fonts.Semibold,
    color: Colors.white,
    alignSelf: 'center',
    paddingVertical: 14,
  },
  subtitleContainer: {
    backgroundColor: Colors.policyContainer,
    height: 460,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  policyText1: {
    fontSize: 14,
    fontFamily: Fonts.Regular,
    paddingHorizontal: 10,
    lineHeight: 18,
    paddingTop: 16,
  },
  bottomline: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 14,
  },
  bottomline1: {
    color: Colors.primary,
    marginLeft: 5,
    fontSize: 12,
    fontFamily: Fonts.Medium,
    marginTop: 8,
  },
  bottomline2: {
    color: Colors.buttonbgcolor,
    fontSize: 12,
    fontFamily: Fonts.Medium,
    marginTop: 8,
  },
  checkBox: {
    width: 20,
    height: 20,
    marginTop: 6,
    marginLeft: 10,
  },
});

export default Policy;
