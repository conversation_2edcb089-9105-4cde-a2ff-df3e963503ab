import React, {useContext, useEffect, useState} from 'react';
import {
  StyleSheet,
  View,
  FlatList,
  ActivityIndicator,
  Text,
} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {images} from '../../../assets/images';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import OngoingTaskList from '../../../component/OngoingTaskList/Ongoingtasklist';
import Searchbar from '../../../component/Searchbar/Searchbar';
import {AuthContext} from '../../../../App';
import {ITask} from '../../../interfaces/ITask';
import {IUser} from '../../../interfaces/IUser';
import firestore from '@react-native-firebase/firestore';
import moment from 'moment';
import {useDebounce} from 'use-debounce';
import {OngoingEmptyState} from '../../../assets/svgIcons';
import {ms, vs} from 'react-native-size-matters';

type Props = NativeStackScreenProps<HomeStackParamsList, 'CompleteTask'>;

const CompleteTask: React.FC<Props> = ({navigation}) => {
  const [completedTasks, setCompletedTasks] = useState<ITask[]>();
  const [isLoading, setIsLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const {userData, userId} = useContext(AuthContext);
  const [value] = useDebounce(searchText, 1000);

  const fetchCompletetedTasks = async () => {
    try {
      const completedTasks = await firestore()
        .collection('Tasks')
        .where('assignedTo', 'array-contains', userId)
        .where('status', '==', 'done')
        .orderBy('date', 'desc')
        .get();
      const tasksData = await Promise.all(
        completedTasks.docs.map(async doc => {
          const taskData = {
            id: doc.id,
            ...doc.data(),
          } as ITask;

          const usersData = await Promise.all(
            taskData.assignedTo.map(async userId => {
              const userDoc = await firestore()
                .collection('Users')
                .doc(userId)
                .get();
              return {
                id: userDoc.id,
                ...userDoc.data(),
              } as IUser;
            }),
          );

          return {
            ...taskData,
            users: usersData,
          };
        }),
      );
      setCompletedTasks(tasksData);
    } catch (error) {
      console.log('Error fetching tasks', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCompletetedTasks();
  }, []);

  const filteredOngoingTasks = completedTasks?.filter(task =>
    task.title.toLowerCase().includes(value.toLowerCase()),
  );
  return (
    <View style={styles.container}>
      <Searchbar
        placeholder={'Search Task'}
        RightIcon={images.searchicon}
        containerStyle={{marginTop: 8, marginHorizontal: 22}}
        value={searchText}
        onChangeText={i => {
          setSearchText(i);
        }}
      />
      {isLoading ? (
        <ActivityIndicator
          size={'large'}
          color={Colors.buttonbgcolor}
          style={{marginTop: 100, alignSelf: 'center'}}
        />
      ) : (
        <FlatList
          showsVerticalScrollIndicator={false}
          data={searchText.length ? filteredOngoingTasks : completedTasks}
          contentContainerStyle={{marginBottom: 20, marginTop: 20}}
          renderItem={({item}) => {
            return (
              <OngoingTaskList
                title={item.title}
                isCompleted={true}
                image={item.users}
                date={moment(item.date.toDate()).format('DD MMM')}
                incentive={item.incentive}
                onPress={() =>
                  navigation.navigate('TaskDetails', {
                    isCompleted: false,
                    item: item,
                    user: userData,
                  })
                }
              />
            );
          }}
          ListEmptyComponent={() => (
            <View style={{alignItems: 'center', marginTop: 40}}>
              <OngoingEmptyState height={130} />
              <Text style={styles.emptyText}>No tasks found</Text>
            </View>
          )}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  emptyText: {
    textAlign: 'center',
    fontFamily: Fonts.Semibold,
    color: Colors.placeholder,
    marginTop: vs(10),
    fontSize: ms(12),
  },
});

export default CompleteTask;
