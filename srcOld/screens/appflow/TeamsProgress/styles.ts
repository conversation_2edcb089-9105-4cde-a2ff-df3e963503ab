import { StyleSheet } from 'react-native';
import { Colors, Fonts } from '../../../utilities/theme/theme';
import { s, vs, ms, mvs } from 'react-native-size-matters';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: s(22),
  },
  circularProgressContainer: {
    alignSelf: 'center',
    marginTop: vs(30),
  },
  avatarLarge: {
    height: ms(82),
    width: ms(82),
    borderRadius: 100,
  },

  userName: {
    color: Colors.buttonbgcolor,
    fontFamily: Fonts.Semibold,
    fontSize: ms(12),
    textAlign: 'center',
    marginTop: 12,
  },
  taskInfo: {
    fontSize: ms(10),
    fontFamily: Fonts.Semibold,
    color: '#A3A2A2',
    textAlign: 'center',
    marginTop: vs(3),
  },
  taskInfoHighlight: {
    color: '#828282',
  },
  progressPercentage: {
    fontFamily: Fonts.Bold,
    fontSize: ms(16),
    color: Colors.primary,
    textAlign: 'center',
    marginTop: vs(3),
  },
  progressPercentageSymbol: {
    fontSize: ms(10),
    color: Colors.primary,
    fontFamily: Fonts.Bold,
  },
  myTeam: {
    fontFamily: Fonts.Medium,
    fontSize: ms(16),
    lineHeight: 24,
    marginTop: vs(30),
    color: Colors.primary,
  },
  teamMembersContainer: {
    gap: ms(26),
    marginTop: vs(29),
    alignSelf: 'center',
    paddingBottom: 20,
  },
  teamMember: {
    marginHorizontal: vs(8),
  },
  avatarSmall: {
    height: ms(50),
    width: ms(50),
    borderRadius: 100,
  },
  teamMemberName: {
    fontSize: ms(10),
    fontFamily: Fonts.Medium,
    lineHeight: 12,
    textAlign: 'center',
    marginTop: vs(6),
    color: Colors.primary,
  },
  teamMemberProgress: {
    fontSize: ms(10),
    fontFamily: Fonts.Medium,
    lineHeight: 12,
    textAlign: 'center',
    color: Colors.primary,
  },
  detailText: {
    color: Colors.buttonbgcolor,
    alignSelf: 'center',
    fontSize: 12,
    fontFamily: Fonts.Semibold,
    lineHeight: 18,
  },
  detailArrow: {
    width: 15,
    height: 10,
    marginLeft: 4,
  },
  detailContainer: {
    flexDirection: 'row',
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
  },
});
