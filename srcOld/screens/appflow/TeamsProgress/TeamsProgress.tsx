import {
  FlatList,
  Image,
  SafeAreaView,
  Text,
  TouchableOpacity,
  View,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import React, {useContext, useEffect, useRef, useState} from 'react';
import {Colors} from '../../../utilities/theme/theme';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {BottomTabParamlist} from '../../../navigation/BottomTabs';
import {ChevronRight} from '../../../assets/svgIcons';
import {images} from '../../../assets/images';
import {CircularProgressBase} from 'react-native-circular-progress-indicator';
import {styles} from './styles';
import {ms} from 'react-native-size-matters';
import {IUser} from '../../../interfaces/IUser';
import firestore from '@react-native-firebase/firestore';
import {AuthContext} from '../../../../App';
import useTasks from '../../../hooks/useTasks';

type Props = NativeStackScreenProps<BottomTabParamlist, 'TeamsProgress'>;
const TeamsProgress: React.FC<Props> = ({navigation}) => {
  const [usersData, setUsersData] = useState<IUser[]>();
  const [isLoading, setIsLoading] = useState(false);
  const {userData} = useContext(AuthContext);
  const {fetchTasks, tasks} = useTasks();

  const circularProgressRef = useRef(null);
  const fetchUsersTasks = async () => {
    try {
      setIsLoading(true);
      const users = await firestore().collection('Users').get();
      const usersData = users.docs.map(
        doc =>
          ({
            id: doc.id,
            ...doc.data(),
          } as IUser),
      );
      setUsersData(usersData);
    } catch (error) {
      console.log('Error fetching tasks', error);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    fetchUsersTasks();
    fetchTasks();
  }, []);
  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{flexGrow: 1}}>
        <SafeAreaView />
        {isLoading ? (
          <ActivityIndicator
            size={'large'}
            color={Colors.buttonbgcolor}
            style={{alignSelf: 'center', marginTop: 100}}
          />
        ) : (
          <>
            <View style={styles.circularProgressContainer}>
              <CircularProgressBase
                initialValue={1}
                ref={circularProgressRef}
                activeStrokeWidth={4}
                inActiveStrokeWidth={4}
                inActiveStrokeOpacity={0.2}
                value={userData.progress || 0}
                radius={ms(50)}
                activeStrokeColor={Colors.buttonbgcolor}
                inActiveStrokeColor={'#CDCDCD'}
                strokeLinecap={'round'}
                maxValue={100}>
                <Image
                  source={
                    userData.profileImage?.url
                      ? {uri: userData.profileImage.url}
                      : images.avatarPlaceholder
                  }
                  style={styles.avatarLarge}
                />
              </CircularProgressBase>
            </View>
            <Text style={styles.userName}>{userData.name}</Text>

            <Text style={styles.taskInfo}>
              Completed{' '}
              <Text style={styles.taskInfoHighlight}>
                {tasks.filter(i => i.status == 'done').length}
              </Text>{' '}
              pending{' '}
              <Text style={styles.taskInfoHighlight}>
                {tasks.filter(i => i.status == 'pending').length}
              </Text>{' '}
            </Text>
            <Text style={styles.progressPercentage}>
              {userData.progress?.toFixed(0) || 0}
              {''}
              <Text style={styles.progressPercentageSymbol}>%</Text>
            </Text>
            <TouchableOpacity
              onPress={() => navigation.navigate('ProgressDetails', {userData})}
              style={styles.detailContainer}>
              <Text style={styles.detailText}>View Detail</Text>
              <Image
                source={images.detailArrow}
                style={styles.detailArrow}
                resizeMode="contain"
              />
            </TouchableOpacity>
            <Text style={styles.myTeam}>My Team</Text>
            <FlatList
              data={usersData}
              numColumns={4}
              showsHorizontalScrollIndicator={false}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.teamMembersContainer}
              renderItem={({item}) => (
                <TouchableOpacity
                  style={styles.teamMember}
                  activeOpacity={0.5}
                  onPress={() =>
                    navigation.navigate('ProgressDetails', {
                      userData: item,
                      isMember: true,
                    })
                  }>
                  <CircularProgressBase
                    initialValue={1}
                    ref={circularProgressRef}
                    activeStrokeWidth={2.5}
                    inActiveStrokeWidth={2.5}
                    inActiveStrokeOpacity={0.2}
                    value={item.progress || 0}
                    radius={ms(30)}
                    activeStrokeColor={Colors.buttonbgcolor}
                    inActiveStrokeColor={'#CDCDCD'}
                    strokeLinecap={'round'}
                    maxValue={100}>
                    <Image
                      source={
                        item.profileImage?.url
                          ? {uri: item.profileImage.url}
                          : images.avatarPlaceholder
                      }
                      style={styles.avatarSmall}
                    />
                  </CircularProgressBase>
                  <Text style={styles.teamMemberName}>{item.name}</Text>
                  <Text style={styles.teamMemberProgress}>
                    {item.progress?.toFixed(0) || 0}%
                  </Text>
                </TouchableOpacity>
              )}
            />
          </>
        )}
      </ScrollView>
    </View>
  );
};

export default TeamsProgress;
