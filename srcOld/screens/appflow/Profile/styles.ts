import {StyleSheet} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: 24,
  },
  profileContainer: {
    width: 90,
    height: 90,
    alignSelf: 'center',
    marginTop: 12,
    borderColor: Colors.buttonbgcolor,
    borderWidth: 3,
    borderRadius: 50,
  },
  nameStyle: {
    color: Colors.primary,
    alignSelf: 'center',
    fontSize: 14,
    fontFamily: Fonts.Medium,
    marginTop: 10,
  },
  emailStyle: {
    color: Colors.buttonbgcolor,
    alignSelf: 'center',
    fontSize: 12,
    fontFamily: Fonts.Medium,
    lineHeight: 12,
    paddingTop: 4,
  },
  modalContainer: {
    backgroundColor: Colors.background,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
  },
  modalHeader: {
    fontFamily: Fonts.Semibold,
    fontSize: 18,
    color: Colors.buttonbgcolor,
    textAlign: 'center',
  },
  divider: {
    height: 1,
    width: '95%',
    backgroundColor: Colors.placeholder,
    marginVertical: 14,
    alignSelf: 'center',
  },
  modalSubHeader: {
    fontFamily: Fonts.Medium,
    fontSize: 16,
    color: Colors.primary,
    textAlign: 'center',
  },
  modalQuestion: {
    fontFamily: Fonts.Regular,
    fontSize: 14,
    color: Colors.primary,
    marginTop: 16,
    textAlign: 'center',
  },
  modalButtonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 21,
  },
  cancelButton: {
    backgroundColor: Colors.background,
  },
  cancelText: {color: Colors.buttonbgcolor},
  logoutButton: {
    marginTop: 12,
  },
  spinnerTextStyle: {
    color: '#000',
    fontFamily: Fonts.Semibold,
  },
});
