import React, {FC, useRef, useState} from 'react';
import {
  Text,
  StyleSheet,
  View,
  Image,
  ScrollView,
  TouchableOpacity,
  Linking,
} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {images} from '../../../assets/images';
import {Button} from '../../../component/Button/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomTabs';
import moment from 'moment';
import ImageView from 'react-native-image-viewing';
import Clipboard from '@react-native-clipboard/clipboard';

type Props = NativeStackScreenProps<
  HomeStackParamsList & BottomTabParamlist,
  'TaskDetails'
>;

const TaskDetailScreen: FC<Props> = ({route, navigation}) => {
  const {isCompleted, item} = route.params;
  const [visible, setIsVisible] = useState(false);
  const [linkCopied, setLinkCopied] = useState(false);

  const imagesToShow = item.attachmentUrl ? [{uri: item.attachmentUrl}] : [];

  const copyToClipboard = () => {
    Clipboard.setString(item.link);
    setLinkCopied(true);
  };

  const handleClick = () => {
    Linking.canOpenURL(item?.link).then(supported => {
      if (supported) {
        Linking.openURL(item.link);
      }
    });
  };

  return (
    <View style={styles.container}>
      <Text style={styles.Title}>{item.title}</Text>

      <View style={styles.subContainer}>
        {/* {Calendar Container} */}
        <View style={styles.calendarContainer}>
          <Image
            source={images.calendarcontainer}
            style={styles.imgcontainer}
          />
          <View style={styles.textCont}>
            <Text style={styles.dateText}>Due Date</Text>
            <Text style={styles.date}>
              {moment(item.date.toDate()).format('DD MMMM')}
            </Text>
          </View>
        </View>
        {/* {Assign to Container} */}
        <View style={styles.assignContainer}>
          <Image
            source={images.assigntocontainer}
            style={styles.imgcontainer}
          />
          <View style={styles.textCont}>
            <Text style={styles.AssignText}>Assigned To</Text>
            <View style={{marginTop: 7}}>
              <ScrollView horizontal>
                {item.users?.map((img, index) => (
                  <Image
                    key={index}
                    source={
                      img.profileImage?.url
                        ? {uri: img.profileImage.url}
                        : images.avatarPlaceholder
                    }
                    style={[
                      styles.assignicon,
                      {marginLeft: index == 0 ? 0 : -5},
                    ]}
                  />
                ))}
              </ScrollView>
            </View>
          </View>
        </View>
      </View>

      <Text style={styles.TaskdetailTitle}>Task Details</Text>
      <Text style={styles.detail}>{item.details}</Text>

      {item.link ? (
        <View>
          <Text style={styles.TaskdetailTitle}>Attached Link</Text>
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}>
            <TouchableOpacity
              activeOpacity={0.7}
              style={{width: '80%'}}
              onPress={handleClick}>
              <Text style={[styles.detail, {color: 'blue'}]}>{item.link}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={{
                paddingVertical: 8,
                alignItems: 'center',
                width: 60,
              }}
              onPress={copyToClipboard}
              activeOpacity={0.5}>
              <Image
                source={images.copyIcon}
                style={[styles.imgcontainer, {width: 30, height: 30}]}
                tintColor={linkCopied ? 'green' : Colors.lightcolor}
              />
              <Text
                style={[
                  styles.date,
                  {color: linkCopied ? 'green' : Colors.lightcolor},
                ]}>
                {linkCopied ? 'Copied' : 'Copy'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      ) : null}

      {isCompleted ? (
        <View style={styles.ProgressContainer}>
          <Text style={styles.TaskProgress}>Task Progress</Text>
          <View style={styles.completeContainer}>
            <Text style={styles.CompText}>Completed</Text>
            <Image style={styles.CompImg} source={images.completeicon} />
          </View>
        </View>
      ) : (
        <View>
          <View style={styles.ProgressContainer}>
            <Text style={styles.TaskProgress}>Task Progress</Text>

            {item.status == 'done' ? (
              <View style={styles.completeContainer}>
                <Text style={styles.CompText}>Completed</Text>
                <Image style={styles.CompImg} source={images.completeicon} />
              </View>
            ) : (
              <View style={styles.completeContainer}>
                <Text style={styles.CompText}>Pending</Text>
                <Image
                  style={[styles.CompImg, {tintColor: Colors.buttonbgcolor}]}
                  source={images.hourglass}
                />
              </View>
            )}
          </View>

          {item.attachmentUrl ? (
            item.attachmentType === 'image' ? (
              <>
                <Text style={styles.attachmentText}>Attachment</Text>
                <TouchableOpacity onPress={() => setIsVisible(true)}>
                  <Image
                    source={{uri: item.attachmentUrl}}
                    style={styles.imgStyle}
                  />
                </TouchableOpacity>
                <ImageView
                  images={imagesToShow}
                  imageIndex={0}
                  visible={visible}
                  onRequestClose={() => setIsVisible(false)}
                />
              </>
            ) : item.attachmentType === 'pdf' ? (
              <View>
                <Text style={styles.attachmentText}>Attachment</Text>
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate('PdfScreen', {
                      attachmentUrl: item.attachmentUrl,
                    });
                  }}>
                  <Image source={images.attachment} style={styles.pdfStyle} />
                </TouchableOpacity>
              </View>
            ) : null
          ) : null}

          <View style={{marginTop: 80}}>
            <Button
              title="Add Task"
              onPress={() => navigation.navigate('Addtask')}
            />
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  imgStyle: {
    width: 75,
    height: 75,
    marginTop: 15,
    borderWidth: 2,
    padding: 10,
    borderColor: Colors.buttonbgcolor,
  },
  pdfStyle: {
    width: 60,
    height: 60,
    marginTop: 15,
  },
  attachmentText: {
    fontSize: 12,
    fontFamily: Fonts.Semibold,
    color: Colors.primary,
    marginTop: 30,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: 22,
    paddingVertical: 22,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  lefticon: {
    width: 20,
    height: 20,
  },
  Righticon: {
    width: 20,
    height: 20,
  },
  screenTitle: {
    color: Colors.primary,
    fontSize: 12,
    fontFamily: Fonts.Medium,
  },
  Title: {
    color: Colors.primary,
    fontSize: 16,
    fontFamily: Fonts.Semibold,
    marginTop: 20,
  },
  subContainer: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 25,
  },
  imgcontainer: {
    width: 41,
    height: 41,
  },
  calendarContainer: {
    flexDirection: 'row',
  },
  textCont: {
    flexDirection: 'column',
    marginLeft: 10,
  },
  assignicon: {
    width: 16,
    height: 16,
    borderWidth: 1,
    borderColor: Colors.buttonbgcolor,
    borderRadius: 10,
  },
  assignContainer: {
    flexDirection: 'row',
    marginLeft: 20,
  },
  dateText: {
    color: '#8CAAB9',
    fontSize: 10,
    fontFamily: Fonts.Regular,
  },
  date: {
    color: Colors.lightcolor,
    fontSize: 12,
    fontFamily: Fonts.Semibold,
  },
  AssignText: {
    color: '#8CAAB9',
    fontSize: 10,
    fontFamily: Fonts.Regular,
  },
  TaskdetailTitle: {
    fontSize: 12,
    fontFamily: Fonts.Semibold,
    color: Colors.primary,
    marginTop: 25,
  },
  detail: {
    fontSize: 12,
    fontFamily: Fonts.Regular,
    color: Colors.primary,
    lineHeight: 20,
  },
  TaskProgress: {
    fontSize: 12,
    fontFamily: Fonts.Semibold,
    color: Colors.primary,
    marginRight: 30,
  },
  ProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 30,
  },
  circularProgressContainer: {
    alignSelf: 'center',
  },
  textPersent: {
    fontSize: 10,
    fontFamily: Fonts.Semibold,
    color: Colors.primary,
  },
  completeContainer: {
    flexDirection: 'row',
  },
  CompText: {
    fontSize: 10,
    fontFamily: Fonts.Medium,
    color: Colors.primary,
  },
  CompImg: {
    width: 16,
    height: 13,
    marginLeft: 10,
  },
  pdfImg: {
    width: 40,
    height: 40,
  },
});

export default TaskDetailScreen;
