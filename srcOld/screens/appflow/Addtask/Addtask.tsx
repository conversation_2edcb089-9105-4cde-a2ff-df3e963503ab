import React, {FC, useContext, useState} from 'react';
import {
  Text,
  View,
  TouchableOpacity,
  ScrollView,
  Image,
  FlatList,
} from 'react-native';
import Input from '../../../component/Input/Input';
import {
  AddIcon,
  CalendarIcon,
  ClockIcon,
  CrossIcon,
  ModalClose,
} from '../../../assets/svgIcons';
import Checkbox from '../../../component/Checkbox/Checkbox';
import DatePicker from 'react-native-date-picker';
import moment from 'moment';
import {images} from '../../../assets/images';
import {Button} from '../../../component/Button/Button';
import {styles} from './styles';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {BottomTabParamlist} from '../../../navigation/BottomTabs';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import Modal from 'react-native-modal';
import firestore from '@react-native-firebase/firestore';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {IUser} from '../../../interfaces/IUser';
import SuccessModal from '../../../component/SuccessModal/SuccessModal';
import ImageCropPicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import storage from '@react-native-firebase/storage';
import {AuthContext} from '../../../../App';
import DocumentPicker, {
  types,
  DirectoryPickerResponse,
  DocumentPickerResponse,
} from 'react-native-document-picker';

type Props = NativeStackScreenProps<BottomTabParamlist, 'Addtask'>;

const Addtask: FC<Props> = ({navigation}) => {
  const [dateOpen, setDateOpen] = useState(false);
  const [timeOpen, setTimeOpen] = useState(false);
  const [isSelected, setSelected] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [employeesData, setEmployeesData] = useState<IUser[]>([]);
  const [selectedEmployees, setSelectedEmployees] = useState<IUser[]>([]);
  const [tempSelectedEmployees, setTempSelectedEmployees] = useState<IUser[]>(
    [],
  );
  const [refreshKey, setRefreshKey] = useState(0);
  const [isButtonLoading, setButtonLoading] = useState(false);
  const [isSuccessModalVisible, setSuccessModalVisible] = useState(false);
  const [isAttachmentModalVisible, setAttachmentModalVisible] = useState(false);
  const [selectedImage, setSelectedImage] = useState<ImageOrVideo>();
  const [selectedDocument, setSelectedDocument] = useState<
    DocumentPickerResponse | undefined | null
  >();
  const {userId} = useContext(AuthContext);

  const validationSchema = Yup.object().shape({
    taskTitle: Yup.string().required('Task Title is required'),
    taskDetails: Yup.string().required('Task Details are required'),
    assignedTo: Yup.array()
      .of(Yup.string())
      .required('Assigned To is required'),
    link: Yup.string().url('Invalid URL'),
    date: Yup.date().required('Date is required'),
    time: Yup.date().required('Time is required'),
    rating: Yup.number()
      .max(100, 'Incentive cannot exceed 100')
      .typeError('Rating must be a number'),
  });

  const onAddTask = async () => {
    try {
      setButtonLoading(true);
      let url;
      if (selectedImage) {
        url = await uploadImage(selectedImage?.path || '');
      } else if (selectedDocument) {
        url = await uploadImage(selectedDocument?.fileCopyUri || '');
      }
      const payload = {
        title: formik.values.taskTitle,
        details: formik.values.taskDetails,
        assignedTo: formik.values.assignedTo,
        status: 'pending',
        time: formik.values.time,
        date: formik.values.date,
        link: formik.values.link,
        incentive: formik.values.rating,
        createdAt: new Date(),
        updatedAt: new Date(),
        attachmentUrl: url ? url : '',
        attachmentType: selectedImage ? 'image' : 'pdf',
        createdBy: userId,
      };
      await firestore()
        .collection('Tasks')
        .add(payload)
        .then(() => {
          formik.resetForm();
          setRefreshKey(prevKey => prevKey + 1);
          setSelectedEmployees([]);
          setTempSelectedEmployees([]);
          setSelected(false);
          setSuccessModalVisible(true);
          setSelectedDocument(undefined);
          setSelectedImage(undefined);
          setTimeout(() => {
            setSuccessModalVisible(false);
            navigation.goBack();
          }, 2000);
        });
    } catch (error) {
      console.log('Error creating tasks:', error);
    } finally {
      setButtonLoading(false);
    }
  };
  const getEmployees = async () => {
    try {
      const employeeSnapshot = await firestore().collection('Users').get();
      const employees = employeeSnapshot.docs.map(
        doc =>
          ({
            id: doc.id,
            ...doc.data(),
          } as IUser),
      );
      setEmployeesData(employees);
    } catch (error) {
      console.log('Error fetching employees', error);
    }
  };

  const onOkPress = () => {
    setSelectedEmployees(tempSelectedEmployees);
    formik.setFieldValue(
      'assignedTo',
      tempSelectedEmployees.map(emp => emp.id),
    );
    setIsVisible(false);
  };
  const onCheckboxPress = (item: IUser) => {
    if (tempSelectedEmployees.some(emp => emp.id === item.id)) {
      setTempSelectedEmployees(
        tempSelectedEmployees.filter(emp => emp.id !== item.id),
      );
    } else {
      setTempSelectedEmployees([...tempSelectedEmployees, item]);
    }
  };
  const formik = useFormik({
    initialValues: {
      taskTitle: '',
      taskDetails: '',
      assignedTo: [],
      link: '',
      rating: '',
      date: new Date(),
      time: new Date(),
    },
    validationSchema: validationSchema,
    validateOnMount: true,
    onSubmit: onAddTask,
  });

  const onCrossIcon = (item: IUser) => {
    setSelectedEmployees(selectedEmployees.filter(emp => emp.id !== item.id));
    setTempSelectedEmployees(
      selectedEmployees.filter(emp => emp.id !== item.id),
    );
    formik.setFieldValue(
      'assignedTo',
      formik.values.assignedTo.filter(id => id !== item.id),
    );
  };
  const onAssignPress = () => {
    getEmployees();
    setIsVisible(true);
  };
  const handlePictureAttachment = () => {
    ImageCropPicker.openPicker({
      cropping: true,
      multiple: false,
      compressImageQuality: 0.5,
    })
      .then(image => {
        setAttachmentModalVisible(false);
        setSelectedImage(image);
      })
      .catch(error => {
        console.log('Image picker error:', error);
      });
  };
  const uploadImage = async (file: string) => {
    try {
      const randomId = Math.floor(Math.random() * 1000000);

      const filePath = `users/${userId}/attachments/${userId}_${randomId}_img`;
      const reference = storage().ref(filePath);
      await reference.putFile(file);
      return reference.getDownloadURL();
    } catch (error) {
      console.log('Image upload error:', error);
    }
  };
  const handleDocumentPicker = async () => {
    try {
      const pickerResult = await DocumentPicker.pickSingle({
        presentationStyle: 'fullScreen',
        copyTo: 'cachesDirectory',
        type: [types.pdf],
      });
      setSelectedDocument(pickerResult);
      setAttachmentModalVisible(false);
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <View key={refreshKey} style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 40}}>
        <Input
          placeholder="Write the task title here"
          title="Task Title"
          titleStyle={styles.titleStyle}
          style={{flex: 1}}
          onChangeText={formik.handleChange('taskTitle')}
          onBlur={formik.handleBlur('taskTitle')}
          value={formik.values.taskTitle}
        />
        <Input
          placeholder="Write the task details here."
          title="Task Details"
          titleStyle={styles.titleStyle}
          multiline
          textinputStyles={styles.taskDetailStyle}
          textAlignVertical="top"
          style={styles.taskDetailInput}
          onChangeText={formik.handleChange('taskDetails')}
          onBlur={formik.handleBlur('taskDetails')}
          value={formik.values.taskDetails}
        />
        <Text style={styles.titleStyle}>
          Add Incentive{' '}
          <Text style={styles.subTitleStyle}>
            One star represent one dollar.
          </Text>
        </Text>

        <Input
          placeholder="Add Incentive (Optional)"
          keyboardType="numeric"
          titleStyle={styles.titleStyle}
          style={{flex: 1}}
          onChangeText={formik.handleChange('rating')}
          onBlur={formik.handleBlur('rating')}
          value={formik.values.rating.toString()}
          maxLength={100}
        />

        <Text style={styles.titleStyle}>Assign to</Text>

        <View style={styles.searchContainer}>
          <View style={styles.listContainer}>
            {!selectedEmployees.length ? (
              <TouchableOpacity
                style={styles.addContainer}
                onPress={onAssignPress}>
                <Text style={styles.addText}>
                  Click on + to assign the task to team members
                </Text>
              </TouchableOpacity>
            ) : (
              selectedEmployees.map(item => {
                return (
                  <View style={styles.searchInput} key={item.id}>
                    <View
                      key={item.id}
                      style={[styles.rowContainer, {padding: 6}]}>
                      <Image
                        source={
                          item.profileImage?.url
                            ? {uri: item.profileImage.url}
                            : images.avatarPlaceholder
                        }
                        style={styles.imageStyle}
                      />
                      <Text style={styles.employeeName}>{item.name}</Text>
                      <TouchableOpacity onPress={() => onCrossIcon(item)}>
                        <CrossIcon />
                      </TouchableOpacity>
                    </View>
                  </View>
                );
              })
            )}
          </View>

          <TouchableOpacity onPress={onAssignPress}>
            <AddIcon height={32} />
          </TouchableOpacity>
        </View>
        <Text style={styles.titleStyle}>Time & Date</Text>
        <View style={styles.timeDateContainer}>
          <TouchableOpacity
            style={styles.rowContainer}
            onPress={() => setTimeOpen(true)}>
            <ClockIcon />
            <View style={styles.timeContainer}>
              <Text style={styles.timeText}>
                {moment(formik.values.time).format('hh:mm a')}
              </Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.rowContainer}
            onPress={() => setDateOpen(true)}>
            <CalendarIcon />
            <View style={styles.timeContainer}>
              <Text style={styles.timeText}>
                {moment(formik.values.date).format('DD/MM/YYYY')}
              </Text>
            </View>
          </TouchableOpacity>
        </View>
        <Input
          placeholder="Paste Link Here"
          textinputStyles={styles.pasteContainerStyle}
          style={{flex: 1, marginRight: 20}}
          RightIcon={images.attachmentIcon}
          onChangeText={formik.handleChange('link')}
          onBlur={formik.handleBlur('link')}
          value={formik.values.link}
          onRightIconPress={() => setAttachmentModalVisible(true)}
        />
        {(selectedDocument || selectedImage) && (
          <View style={{flex: 1}}>
            <TouchableOpacity
              style={styles.crossButton}
              onPress={() => {
                setSelectedImage(undefined);
                setSelectedDocument(undefined);
              }}>
              <Image source={images.crossIcon} style={styles.crossIcon} />
            </TouchableOpacity>
            <Image
              source={
                selectedImage
                  ? {uri: selectedImage?.path}
                  : selectedDocument
                  ? images.attachment
                  : null
              }
              style={[
                styles.attachmentimage,
                {
                  tintColor: selectedImage
                    ? undefined
                    : selectedDocument
                    ? Colors.buttonbgcolor
                    : undefined,
                },
              ]}
            />
          </View>
        )}
        <Button
          title="Add Task"
          containerStyle={styles.buttonStyle}
          onPress={formik.handleSubmit}
          disabled={isButtonLoading || !formik.isValid}
          isLoading={isButtonLoading}
        />
      </ScrollView>
      <DatePicker
        modal
        open={timeOpen}
        date={formik.values.time}
        mode="time"
        onConfirm={selectedTime => {
          setTimeOpen(false);
          formik.setFieldValue('time', selectedTime);
        }}
        onCancel={() => {
          setTimeOpen(false);
        }}
      />
      <DatePicker
        modal
        open={dateOpen}
        date={formik.values.date}
        mode="date"
        onConfirm={selectedDate => {
          setDateOpen(false);
          formik.setFieldValue('date', selectedDate);
        }}
        onCancel={() => {
          setDateOpen(false);
        }}
      />
      <Modal
        isVisible={isVisible}
        onBackdropPress={() => setIsVisible(false)}
        style={{margin: 0, justifyContent: 'flex-end'}}>
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={{marginBottom: 12}}
            onPress={() => setIsVisible(false)}>
            <ModalClose />
          </TouchableOpacity>
          <FlatList
            data={employeesData}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{paddingBottom: 100, paddingTop: 10}}
            keyExtractor={({id}) => id}
            renderItem={({item}) => (
              <TouchableOpacity
                style={[
                  styles.rowContainer,
                  styles.modalRow,
                  {
                    borderColor: tempSelectedEmployees.some(
                      emp => emp.id === item.id,
                    )
                      ? Colors.buttonbgcolor
                      : '#D7D7D7',
                  },
                ]}
                onPress={() => onCheckboxPress(item)}>
                <View style={styles.rowContainer}>
                  <Image
                    source={
                      item.profileImage?.url
                        ? {uri: item.profileImage.url}
                        : images.avatarPlaceholder
                    }
                    style={styles.avatarImage}
                  />
                  <View style={{marginLeft: 8}}>
                    <Text style={styles.nameText}>{item.name}</Text>
                    <Text style={styles.designation}>{item.userType}</Text>
                  </View>
                </View>
                <Checkbox
                  isSelected={tempSelectedEmployees.some(
                    emp => emp.id === item.id,
                  )}
                  onPress={() => onCheckboxPress(item)}
                  containerStyle={{
                    marginTop: 0,
                    borderColor: tempSelectedEmployees.some(
                      emp => emp.id === item.id,
                    )
                      ? Colors.buttonbgcolor
                      : '#D7D7D7',
                  }}
                />
              </TouchableOpacity>
            )}
          />

          <Button
            title="Done"
            onPress={onOkPress}
            containerStyle={styles.doneBtn}
          />
        </View>
      </Modal>
      <SuccessModal
        isVisible={isSuccessModalVisible}
        onClose={() => setSuccessModalVisible(false)}
        message="TASK ADDED"
      />
      <Modal
        isVisible={isAttachmentModalVisible}
        onBackdropPress={() => setAttachmentModalVisible(false)}
        style={styles.attachmentModal}>
        <View style={styles.attachmentModalContainer}>
          <TouchableOpacity
            style={styles.attachmentButton}
            onPress={handleDocumentPicker}>
            <Image source={images.attachment} style={styles.attachmentImage} />
            <Text style={styles.attachmentText}>Document</Text>
          </TouchableOpacity>
          <View style={styles.divider} />
          <TouchableOpacity
            style={styles.attachmentButton}
            onPress={handlePictureAttachment}>
            <Image
              source={images.pictureAttachment}
              style={styles.attachmentImage}
            />
            <Text style={styles.attachmentText}>Picture</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
};

export default Addtask;
