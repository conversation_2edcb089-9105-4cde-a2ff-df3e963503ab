import React from 'react';
import {Text, StyleSheet, View, Image} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {images} from '../../../assets/images';
import {Button} from '../../../component/Button/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {vs} from 'react-native-size-matters';
type Props = NativeStackScreenProps<HomeStackParamsList, 'UpdatedPassword'>;

const UpdatedPassword: React.FC<Props> = ({navigation}) => {
  return (
    <View style={styles.container}>
      <View />
      <View>
        <Image style={styles.imgStyle} source={images.recoverpasswordicon} />
        <Text style={styles.titleStyle}>Success</Text>
        <Text style={styles.subTitle}>
          The password has been successfully updated.
        </Text>
      </View>
      <Button
        title={'Done'}
        onPress={() => navigation.navigate('BottomTabs')}
        containerStyle={{
          backgroundColor: 'white',
          marginTop: 10,
          marginBottom: vs(60),
        }}
        textContainer={{color: Colors.buttonbgcolor}}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.buttonbgcolor,
    paddingHorizontal: 18,
    justifyContent: 'space-between',
  },
  imgStyle: {
    height: 240,
    width: 240,
    alignSelf: 'center',
    // marginTop: 145,
  },
  titleStyle: {
    fontSize: 20,
    fontFamily: Fonts.Bold,
    alignSelf: 'center',
    color: Colors.white,
    marginTop: 50,
  },
  subTitle: {
    fontSize: 14,
    fontFamily: Fonts.Medium,
    lineHeight: 22,
    textAlign: 'center',
    color: Colors.subTextlightColor,
    marginTop: 10,
  },
});

export default UpdatedPassword;
