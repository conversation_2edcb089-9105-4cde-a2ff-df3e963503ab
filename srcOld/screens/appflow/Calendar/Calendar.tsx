import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useEffect, useState} from 'react';
import {
  Text,
  View,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import {BottomTabParamlist} from '../../../navigation/BottomTabs';
import CalendarStrip from 'react-native-calendar-strip';
import moment, {Moment} from 'moment';
import AssignedTaskComponent from '../../../component/AssignedTaskComponent/AssignedTaskComponent';
import {styles} from './styles';
import {ITask} from '../../../interfaces/ITask';
import {Colors} from '../../../utilities/theme/theme';
import {MarkDone, OngoingEmptyState} from '../../../assets/svgIcons';
import useTasks from '../../../hooks/useTasks';
import {GestureHandlerRootView, Swipeable} from 'react-native-gesture-handler';

type Props = NativeStackScreenProps<BottomTabParamlist, 'Calendar'>;

const Calendar: React.FC<Props> = ({navigation}) => {
  const [selectedDate, setSelectedDate] = useState(moment());

  const {fetchTasks, isLoading, tasks, markTaskAsDone} = useTasks();

  useEffect(() => {
    fetchTasks();
  }, []);

  const filterTasksByDate = (tasks: ITask[], date: Moment) =>
    tasks.filter(task => moment(task.date.toDate())['isSame'](date, 'day'));

  const renderTaskList = (tasks: ITask[], title: string) => (
    <>
      {tasks.length ? (
        <>
          <Text style={styles.sectionHeader}>{title}</Text>
          {tasks.map(item => (
            <Swipeable
              renderRightActions={() => (
                <TouchableOpacity
                  style={styles.rightActionContainer}
                  onPress={() => markTaskAsDone(item.id)}>
                  <MarkDone />
                </TouchableOpacity>
              )}
              key={item.id}>
              <AssignedTaskComponent
                key={item.id}
                onPress={() =>
                  navigation.navigate('TaskDetails', {
                    isCompleted: false,
                    item: item,
                  })
                }
                item={item}
                pending={item.status == 'done' ? true : false}
                delayed={
                  moment(item.date.toDate()).isBefore(new Date()) &&
                  item.status !== 'done'
                }
                containerStyle={styles.taskContainer}
              />
            </Swipeable>
          ))}
        </>
      ) : (
        <View style={styles.emptyContainer}>
          <OngoingEmptyState height={130} />
          <Text style={styles.emptyText}>No tasks on this day</Text>
        </View>
      )}
    </>
  );

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <View style={styles.container}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollViewContent}>
          <View style={{marginTop: 30}}>
            <CalendarStrip
              onDateSelected={date => setSelectedDate(date)}
              selectedDate={selectedDate}
              useIsoWeekday={false}
              scrollable
              scrollToOnSetSelectedDate
              style={styles.calendarStrip}
              dateNameStyle={styles.dateName}
              dateNumberStyle={styles.dateNumber}
              dayContainerStyle={styles.dayContainer}
              maxDayComponentSize={80}
              minDayComponentSize={65}
              dayComponentHeight={86}
              highlightDateContainerStyle={styles.highlightDateContainer}
              highlightDateNumberStyle={styles.highlightDateNumber}
              highlightDateNameStyle={styles.highlightDateName}
              iconLeftStyle={styles.calendarLeftIcon}
              iconRightStyle={styles.calendarRightIcon}
              calendarHeaderContainerStyle={styles.calendarHeaderContainer}
              calendarHeaderStyle={styles.calendarHeader}
              upperCaseDays={false}
              calendarHeaderFormat={'MMMM'}
            />
          </View>
          {isLoading ? (
            <ActivityIndicator
              size={'large'}
              color={Colors.buttonbgcolor}
              style={{alignItems: 'center', marginTop: 100}}
            />
          ) : (
            <View style={styles.innerContainer}>
              {renderTaskList(filterTasksByDate(tasks, selectedDate), 'Tasks')}
            </View>
          )}
        </ScrollView>
      </View>
    </GestureHandlerRootView>
  );
};

export default Calendar;
