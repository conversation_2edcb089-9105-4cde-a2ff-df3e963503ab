import * as React from 'react';
import {
  Image,
  View,
  StyleSheet,
  TouchableOpacity,
  ImageSourcePropType,
} from 'react-native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import Profile from '../screens/appflow/Profile/Profile';
import {images} from '../assets/images';
import {Colors, Fonts} from '../utilities/theme/theme';
import {AddTaskIcon, ArrowLeftIcon} from '../assets/svgIcons';
import {useNavigation} from '@react-navigation/native';
import {ITask} from '../interfaces/ITask';
import {s} from 'react-native-size-matters';
import TaskStackNavigation from './TaskStackNavigation';
import ClientDashboard from '../screens/appflow/ClientDashboard/ClientDashboard';
import {categoryStructure} from '../interfaces/IIssue';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import AnimatedIcon from '../component/common/TabBarIcon/AnimatedIcon';
import {IUser} from '../interfaces/IUser';

export type BottomTabParamlist = {
  TaskStackNavigatorStack: undefined;
  ClientDashboard: undefined;
  Calendar: undefined;
  TeamsProgress: undefined;
  Profile: undefined;
  ProgressDetails: {userData: IUser; tasks: ITask[]};
  EmployeesModal: undefined;
  CreateNewTask: undefined;
  SelectCategory: undefined;
  SelectAttachment: {selectedCatIds: string[]; category: categoryStructure};
  Notification: undefined;
  IssueDetails: undefined;
  TaskDetails: {taskDetails: ITask; staffUser?: IUser};
};

const Tab = createBottomTabNavigator<BottomTabParamlist>();

function ClientBottomTabs() {
  const navigation = useNavigation();

  const renderHeaderLeft = () => (
    <TouchableOpacity
      onPress={() => navigation.goBack()}
      style={styles.headerLeft}>
      <ArrowLeftIcon />
    </TouchableOpacity>
  );

  const renderIcon = (
    activeIcon: ImageSourcePropType,
    inActiveIcon: ImageSourcePropType,
    focused: boolean,
  ) => {
    const scale = useSharedValue(focused ? 1.2 : 1);

    React.useEffect(() => {
      scale.value = withTiming(focused ? 1.2 : 1, {duration: 200});
    }, [focused]);

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{scale: scale.value}],
    }));

    return (
      <Animated.Image
        source={focused ? activeIcon : inActiveIcon}
        style={[styles.icon, animatedStyle]}
        resizeMode="contain"
      />
    );
  };

  return (
    <Tab.Navigator
      initialRouteName="ClientDashboard"
      screenOptions={{
        tabBarHideOnKeyboard: true,
        tabBarStyle: styles.tabBarStyle,
        tabBarLabelStyle: styles.tabBarLabelStyle,
        headerStyle: styles.headerStyle,
        headerTitleStyle: styles.headerTitleStyle,
        headerTitleAlign: 'center',
        headerShown: true,
        headerLeft: renderHeaderLeft,
        tabBarInactiveTintColor: '#171717',
        tabBarActiveTintColor: Colors.buttonbgcolor,
      }}>
      <Tab.Screen
        name="ClientDashboard"
        component={ClientDashboard}
        options={{
          tabBarLabel: 'Home',
          headerShown: false,
          tabBarIcon: ({focused}) => (
            <AnimatedIcon
              activeIcon={images.HomeFill}
              inActiveIcon={images.HomeStroke}
              focused={focused}
            />
          ),
        }}
      />

      <Tab.Screen
        name="TaskStackNavigatorStack"
        component={TaskStackNavigation}
        options={{
          headerShown: false,
          tabBarLabel: '',
          tabBarIcon: ({focused}) => (
            <View style={styles.addTaskStyle}>
              <Image
                style={[styles.icon, {tintColor: Colors.white}]}
                source={images.addtaskicon}
                resizeMode="contain"
              />
            </View>
          ),
        }}
      />

      <Tab.Screen
        name="Profile"
        component={Profile}
        options={{
          headerShadowVisible: false,
          tabBarLabel: 'Profile',
          headerTitle: '',
          tabBarIcon: ({focused}) => (
            <AnimatedIcon
              activeIcon={images.ProfileFill}
              inActiveIcon={images.ProfileStroke}
              focused={focused}
            />
          ),
        }}
      />
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({
  icon: {
    width: 20,
    height: 20,
  },
  addTaskStyle: {
    width: 46,
    height: 46,
    backgroundColor: Colors.buttonbgcolor,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
  },
  tabBarStyle: {
    paddingTop: 20,
  },
  tabBarLabelStyle: {
    marginTop: 10,
  },
  headerStyle: {
    backgroundColor: Colors.background,
  },
  headerTitleStyle: {
    color: Colors.primary,
    fontSize: 14,
    fontFamily: Fonts.Semibold,
  },
  headerLeft: {
    marginLeft: 24,
  },
  headerRight: {
    marginRight: s(24),
  },
});

export default ClientBottomTabs;
