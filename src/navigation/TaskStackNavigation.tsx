import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {appStyles, Colors} from '../utilities/theme/theme';
import {Image, TouchableOpacity, View} from 'react-native';
import {images} from '../assets/images';
import {categoryStructure} from '../interfaces/IIssue';
import SelectAttachment from '../screens/appflow/IssueCreation/SelectAttachment';
import SelectCategory from '../screens/appflow/IssueCreation/SelectCategory';

export type TaskStackParamList = {
  SelectCategory: undefined;
  SelectAttachment: {selectedCatIds: string[]; category: categoryStructure};
};

const Stack = createNativeStackNavigator<TaskStackParamList>();

const TaskStackNavigation = () => {
  return (
    <Stack.Navigator
      initialRouteName="SelectCategory"
      screenOptions={({navigation}) => ({
        headerShown: true,
        headerTitleAlign: 'center',
        headerShadowVisible: false,
        headerTitleStyle: appStyles.headerTitleStyle,
        headerStyle: appStyles.headerStyle,
        headerLeft: () => (
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Image
              style={{height: 22, width: 22, marginLeft: 8}}
              source={images.leftarrow}
              resizeMode="contain"
            />
          </TouchableOpacity>
        ),
        navigationBarColor: Colors.white,
      })}>
      <Stack.Screen
        name="SelectCategory"
        component={SelectCategory}
        options={{title: 'Select Category', headerLeft: () => <View />}}
      />
      <Stack.Screen
        name="SelectAttachment"
        component={SelectAttachment}
        options={{title: 'New Issue'}}
      />
    </Stack.Navigator>
  );
};

export default TaskStackNavigation;
