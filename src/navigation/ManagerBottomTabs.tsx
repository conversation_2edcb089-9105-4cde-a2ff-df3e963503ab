import * as React from 'react';
import {Image, View, StyleSheet} from 'react-native';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import Profile from '../screens/appflow/Profile/Profile';
import {images} from '../assets/images';
import {Colors, Fonts} from '../utilities/theme/theme';
import {useNavigation} from '@react-navigation/native';
import {ITask} from '../interfaces/ITask';
import {s} from 'react-native-size-matters';
import {categoryStructure} from '../interfaces/IIssue';
import ManagerDashBoard from '../screens/appflow/ManagerDashBoard/ManagerDashBoard';
import Calendar from '../screens/appflow/Calendar/Calendar';
import TeamsProgress from '../screens/appflow/TeamsProgress/TeamsProgress';
import AnimatedIcon from '../component/common/TabBarIcon/AnimatedIcon';
import {IUser} from '../interfaces/IUser';
import CreateTask from '../screens/appflow/CreateTask/CreateTask';

export type BottomTabParamlist = {
  TaskStackNavigatorStack: undefined;
  ClientDashboard: undefined;
  Notification: undefined;
  Profile: undefined;
  CreateNewTask: undefined;
  SelectCategory: undefined;
  SelectAttachment: {selectedCatIds: string[]; category: categoryStructure};
  IssueDetails: undefined;
  ManagerDashBoard: undefined;
  Calendar: undefined;
  TeamsProgress: undefined;
  TaskDetails: {taskDetails: ITask; staffUser?: IUser};
  CreateTask: undefined;
};

const Tab = createBottomTabNavigator<BottomTabParamlist>();

function ManagerBottomTabs() {
  const navigation = useNavigation();

  return (
    <Tab.Navigator
      initialRouteName="ClientDashboard"
      screenOptions={{
        tabBarHideOnKeyboard: true,
        tabBarStyle: styles.tabBarStyle,
        tabBarLabelStyle: styles.tabBarLabelStyle,
        headerStyle: styles.headerStyle,
        headerTitleStyle: styles.headerTitleStyle,
        headerTitleAlign: 'center',
        headerShown: true,
        // headerLeft: renderHeaderLeft,
        tabBarInactiveTintColor: '#171717',
        tabBarActiveTintColor: Colors.buttonbgcolor,
      }}>
      <Tab.Screen
        name="ManagerDashBoard"
        component={ManagerDashBoard}
        options={{
          tabBarLabel: 'Home',
          headerShown: false,
          tabBarIcon: ({focused}) => (
            <AnimatedIcon
              activeIcon={images.HomeFill}
              inActiveIcon={images.HomeStroke}
              focused={focused}
            />
          ),
        }}
      />

      <Tab.Screen
        name="Calendar"
        component={Calendar}
        options={{
          headerShown: true,
          headerTitle: 'Schedule',
          headerTitleAlign: 'center',
          headerShadowVisible: false,
          tabBarLabel: 'Calendar',
          headerTitleStyle: styles.headerTitleStyle,
          tabBarIcon: ({focused}) => (
            <AnimatedIcon
              activeIcon={images.CalendarFill}
              inActiveIcon={images.CalendarStroke}
              focused={focused}
            />
          ),
        }}
      />

      <Tab.Screen
        name="CreateTask"
        component={CreateTask}
        options={{
          headerShown: true,
          tabBarLabel: '',
          tabBarIcon: ({focused}) => (
            <View style={styles.addTaskStyle}>
              <Image
                style={[styles.icon, {tintColor: Colors.white}]}
                source={images.addtaskicon}
                resizeMode="contain"
              />
            </View>
          ),
          headerLeft: () => <View />,
          headerTitle: 'Create New Task',
          headerShadowVisible: false,
        }}
      />

      <Tab.Screen
        name="TeamsProgress"
        component={TeamsProgress}
        options={{
          headerShown: true,
          tabBarLabel: 'My Team',
          headerLeft: () => <View />,
          tabBarIcon: ({focused}) => (
            <AnimatedIcon
              activeIcon={images.MyTeamFill}
              inActiveIcon={images.MyTeamStroke}
              focused={focused}
            />
          ),
          headerShadowVisible: false,
          headerTitle: 'Progress',
        }}
      />

      <Tab.Screen
        name="Profile"
        component={Profile}
        options={{
          headerShadowVisible: false,
          tabBarLabel: 'Profile',
          tabBarIcon: ({focused}) => (
            <AnimatedIcon
              activeIcon={images.ProfileFill}
              inActiveIcon={images.ProfileStroke}
              focused={focused}
            />
          ),
          headerTitle: 'Profile',
        }}
      />
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({
  icon: {
    width: 20,
    height: 20,
  },
  addTaskStyle: {
    width: 40,
    height: 40,
    backgroundColor: Colors.buttonbgcolor,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
  },
  tabBarStyle: {
    paddingTop: 20,
  },
  tabBarLabelStyle: {
    marginTop: 10,
  },
  headerStyle: {
    backgroundColor: Colors.background,
  },
  headerTitleStyle: {
    color: Colors.primary,
    fontSize: 14,
    fontFamily: Fonts.Semibold,
  },
  headerLeft: {
    marginLeft: 24,
  },
  headerRight: {
    marginRight: s(24),
  },
});

export default ManagerBottomTabs;
