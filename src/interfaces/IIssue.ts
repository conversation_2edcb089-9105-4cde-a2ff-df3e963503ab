import {FirebaseFirestoreTypes} from '@react-native-firebase/firestore';

export interface ISubCategory {
  id: string;
  title: string;
  parentCategory?: string;
}
export interface categoryStructure {
  parentCategory: {
    title: string;
    url: string;
  };
  subCategories: string[];
}
export interface ICategory {
  id: string;
  title: string;
  icon: {
    url: string;
  };
  parentCategory: string | null;
  subcategories: ISubCategory[];
}
interface attachemts {
  url: string;
  path: string;
}
export interface IIssue {
  id: string;
  attachment: attachemts[];
  categories: string[];
  note: string;
  createdAt: FirebaseFirestoreTypes.Timestamp;
  updatedAt: FirebaseFirestoreTypes.Timestamp;
  categoryStructure: categoryStructure | null;
  userId?: string;
  managerId?: string;
  status: string;
  priority: 'Low' | 'Medium' | 'High';
  duration: string;
  durationTimeStamp: FirebaseFirestoreTypes.Timestamp;
  assignedStaff: string[];
}
