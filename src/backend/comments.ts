import firestore from '@react-native-firebase/firestore';
import {IComment} from '../interfaces/ITask';

export const addCommentToTask = async (
  taskId: string,
  comment: Omit<IComment, 'id'>,
): Promise<boolean> => {
  try {
    // Add comment to subcollection
    await firestore()
      .collection('Tasks')
      .doc(taskId)
      .collection('comments')
      .add({
        ...comment,
        createdAt: firestore.FieldValue.serverTimestamp(),
      });

    // Update task's updatedAt timestamp
    await firestore().collection('Tasks').doc(taskId).update({
      updatedAt: firestore.FieldValue.serverTimestamp(),
    });

    return true;
  } catch (error) {
    console.error('Error adding comment to task:', error);
    return false;
  }
};

export const subscribeToTaskComments = (
  taskId: string,
  callback: (comments: IComment[]) => void,
) => {
  return firestore()
    .collection('Tasks')
    .doc(taskId)
    .collection('comments')
    .orderBy('createdAt', 'asc')
    .onSnapshot(
      snapshot => {
        const comments: IComment[] = [];
        snapshot.forEach(doc => {
          comments.push({
            id: doc.id,
            ...doc.data(),
          } as IComment);
        });
        callback(comments);
      },
      error => {
        console.error('Error subscribing to task comments:', error);
      },
    );
};
