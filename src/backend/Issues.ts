import {ICategory} from './../interfaces/IIssue';
import firestore, {
  FirebaseFirestoreTypes,
} from '@react-native-firebase/firestore';
import {IIssue} from '../interfaces/IIssue';

/**
 * Fetches pending issues for a manager with optional limit, including enriched category structure.
 * @param managerId - Firestore user ID of the manager
 * @param limit - (optional) max number of issues to fetch
 * @returns Promise<IIssue[]>
 */
export const getIssuesByManagerId = async (
  managerId: string,
  status?: string,
  limit?: number,
): Promise<IIssue[]> => {
  try {
    let query: FirebaseFirestoreTypes.Query = firestore()
      .collection('Issues')
      .where('managerId', '==', managerId)
      .orderBy('createdAt', 'desc');

    // Conditionally apply status filter
    if (status) {
      query = query.where('status', '==', status);
    }

    // Apply limit if provided
    if (limit) {
      query = query.limit(limit);
    }

    const snapshot = await query.get();

    const issues = snapshot.docs.map(doc => ({
      id: doc.id,
      ...(doc.data() as Omit<IIssue, 'id' | 'categoryStructure'>),
    }));

    const allCategoryIds = Array.from(
      new Set(issues.flatMap(issue => issue.categories)),
    );

    // Return early if no categories found
    if (allCategoryIds.length === 0) {
      return issues.map(issue => ({
        ...issue,
        categoryStructure: {
          parentCategory: {title: '', url: ''},
          subCategories: [],
        },
      }));
    }

    // Fetch all referenced categories
    const categorySnapshot = await firestore()
      .collection('Categories')
      .where(firestore.FieldPath.documentId(), 'in', allCategoryIds)
      .get();

    const categoryMap = new Map(
      categorySnapshot.docs.map(doc => [doc.id, {id: doc.id, ...doc.data()}]),
    );

    // Attach category structure to each issue
    const enrichedIssues: IIssue[] = issues.map(issue => {
      let parentCategory = {title: '', url: ''};
      const subCategories: string[] = [];

      for (const catId of issue.categories) {
        const category = categoryMap.get(catId) as ICategory | undefined;
        if (!category) continue;

        if (!category.parentCategory) {
          parentCategory = {
            title: category.title,
            url: category.icon?.url ?? '',
          };
        } else {
          subCategories.push(category.title);
        }
      }

      return {
        ...issue,
        categoryStructure: {
          parentCategory,
          subCategories,
        },
      };
    });

    return enrichedIssues;
  } catch (err) {
    console.error('Error fetching issues by managerId:', err);
    return [];
  }
};
