import {useState, useContext} from 'react';
import firestore from '@react-native-firebase/firestore';
import {AuthContext} from '../../App';
import {ITask} from '../interfaces/ITask';
import {IUser} from '../interfaces/IUser';

const useTasks = () => {
  const {userId, setUserData, userData} = useContext(AuthContext);
  const [tasks, setTasks] = useState<ITask[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchTasks = async (
    status?: 'pending' | 'done',
    orderBy?: string,
    limit = 0,
  ) => {
    let query = firestore()
      .collection('Tasks')
      .where('assignedTo', 'array-contains', userId);

    if (status) {
      query = query.where('status', '==', status);
    }

    if (orderBy) {
      query = query.orderBy(orderBy, 'desc');
    }

    if (limit) {
      query = query.limit(limit);
    }

    const unsubscriber = query.onSnapshot(
      async snapshot => {
        const tasksData: ITask[] = [];
        for (const doc of snapshot.docs) {
          const taskData = {
            id: doc.id,
            ...doc.data(),
          } as ITask;

          const usersDataPromises = taskData.assignedTo.map(async userId => {
            const userDoc = await firestore()
              .collection('Users')
              .doc(userId)
              .get();
            return {
              id: userDoc.id,
              ...userDoc.data(),
            } as IUser;
          });

          const usersData = await Promise.all(usersDataPromises);

          tasksData.push({
            ...taskData,
            users: usersData,
          });
        }

        setTasks(tasksData);

        setIsLoading(false);
      },
      e => {
        console.log('Error', e);
      },
    );

    return () => {
      unsubscriber();
    };
  };

  const fetchTasksById = async (memberId: string) => {
    let query = firestore()
      .collection('Tasks')
      .where('assignedTo', 'array-contains', memberId);

    const unsubscriber = query.onSnapshot(
      async snapshot => {
        const tasksData: ITask[] = [];
        for (const doc of snapshot.docs) {
          const taskData = {
            id: doc.id,
            ...doc.data(),
          } as ITask;

          const usersDataPromises = taskData.assignedTo.map(async memberId => {
            const userDoc = await firestore()
              .collection('Users')
              .doc(memberId)
              .get();
            return {
              id: userDoc.id,
              ...userDoc.data(),
            } as IUser;
          });

          const usersData = await Promise.all(usersDataPromises);

          tasksData.push({
            ...taskData,
            users: usersData,
          });
        }

        setTasks(tasksData);

        setIsLoading(false);
      },
      e => {
        console.log('Error', e);
      },
    );

    return () => {
      unsubscriber();
    };
  };

  const calculateProgress = (tasks: ITask[]) => {
    const totalTasks = tasks.length;
    const completedTasks = tasks.filter(task => task.status === 'done').length;
    const progressPercentage =
      totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
    return progressPercentage;
  };

  const markTaskAsDone = async (taskId: string) => {
    try {
      const newProgress = calculateProgress(tasks);
      await firestore()
        .collection('Tasks')
        .doc(taskId)
        .update({
          status: 'done',
        })
        .then(async () => {
          await firestore().collection('Users').doc(userId).update({
            progress: newProgress,
          });

          setUserData({...userData, progress: newProgress});
        });
    } catch (error) {
      console.log('Error updating task status', error);
    }
  };

  return {
    fetchTasks,
    fetchTasksById,
    tasks,
    isLoading,
    markTaskAsDone,
  };
};

export default useTasks;
