// import {ICategory, IIssue} from './../interfaces/IIssue';
// import {useContext, useEffect, useState} from 'react';
// import firestore from '@react-native-firebase/firestore';
// import {AuthContext} from '../../App';

// const useIssues = () => {
//   const {userId} = useContext(AuthContext);

//   const [pendingIssues, setPendingIssues] = useState<IIssue[]>([]);
//   const [progressIssues, setProgressIssues] = useState<IIssue[]>([]);
//   const [completedIssues, setCompletedIssues] = useState<IIssue[]>([]);
//   const [issueLoading, setIssueLoading] = useState(true);

//   useEffect(() => {
//     if (!userId) return;
//     try {
//       const query = firestore()
//         .collection('Issues')
//         .where('userId', '==', userId)
//         .where('status', '==', 'pending')
//         .orderBy('createdAt', 'desc');

//       const unsubscribe = query.onSnapshot(
//         async snapshot => {
//           const issues = snapshot.docs.map(doc => ({
//             id: doc.id,
//             ...(doc.data() as Omit<IIssue, 'id' | 'categoryStructure'>), // exclude id and categoryStructure for now
//           }));

//           const allCategoryIds = Array.from(
//             new Set(issues.flatMap(issue => issue.categories)),
//           );

//           // Fetch category documents based on all collected IDs
//           const categoryDocs = await firestore()
//             .collection('Categories')
//             .where(firestore.FieldPath.documentId(), 'in', allCategoryIds)
//             .get();

//           const categoryMap = new Map(
//             categoryDocs.docs.map(doc => [doc.id, {id: doc.id, ...doc.data()}]),
//           );

//           // Attach category structure to each issue
//           const issuesWithCategoryStructure: IIssue[] = issues.map(issue => {
//             let parent: any = null;
//             const subCategories: string[] = [];

//             issue.categories.forEach(catId => {
//               const cat = categoryMap.get(catId) as ICategory | undefined;
//               if (!cat) return;
//               if (!cat) return;

//               if (!cat.parentCategory) {
//                 // Category is a parent
//                 parent = {
//                   title: cat.title,
//                   url: cat.icon.url,
//                 };
//               } else {
//                 // It's a child/sub-category
//                 subCategories.push(cat.title);
//               }
//             });

//             return {
//               ...issue,
//               categoryStructure: {
//                 parentCategory: parent,
//                 subCategories,
//               },
//             };
//           });

//           setPendingIssues(issuesWithCategoryStructure);
//           setIssueLoading(false);
//         },
//         err => {
//           console.error('Error fetching completed issues:', err);
//           setIssueLoading(false);
//         },
//       );

//       return () => unsubscribe();
//     } catch (error) {
//     } finally {
//       setIssueLoading(false);
//     }
//   }, [userId]);

//   useEffect(() => {
//     if (!userId) return;

//     const query = firestore()
//       .collection('Issues')
//       .where('userId', '==', userId)
//       .where('status', '==', 'progress')
//       .orderBy('createdAt', 'desc');

//     const unsubscribe = query.onSnapshot(
//       async snapshot => {
//         const issues = snapshot.docs.map(doc => ({
//           id: doc.id,
//           ...(doc.data() as Omit<IIssue, 'id' | 'categoryStructure'>), // exclude id and categoryStructure for now
//         }));

//         const allCategoryIds = Array.from(
//           new Set(issues.flatMap(issue => issue.categories)),
//         );

//         // Fetch category documents based on all collected IDs
//         const categoryDocs = await firestore()
//           .collection('Categories')
//           .where(firestore.FieldPath.documentId(), 'in', allCategoryIds)
//           .get();

//         const categoryMap = new Map(
//           categoryDocs.docs.map(doc => [doc.id, {id: doc.id, ...doc.data()}]),
//         );

//         // Attach category structure to each issue
//         const issuesWithCategoryStructure: IIssue[] = issues.map(issue => {
//           let parent: any = null;
//           const subCategories: string[] = [];

//           issue.categories.forEach(catId => {
//             const cat = categoryMap.get(catId) as ICategory | undefined;
//             if (!cat) return;
//             if (!cat) return;

//             if (!cat.parentCategory) {
//               // Category is a parent
//               parent = {
//                 title: cat.title,
//                 url: cat.icon.url,
//               };
//             } else {
//               // It's a child/sub-category
//               subCategories.push(cat.title);
//             }
//           });

//           return {
//             ...issue,
//             categoryStructure: {
//               parentCategory: parent,
//               subCategories,
//             },
//           };
//         });

//         setProgressIssues(issuesWithCategoryStructure);
//         setIssueLoading(false);
//       },
//       err => {
//         console.error('Error fetching completed issues:', err);
//         setIssueLoading(false);
//       },
//     );

//     return () => unsubscribe();
//   }, [userId]);

//   useEffect(() => {
//     if (!userId) return;

//     const query = firestore()
//       .collection('Issues')
//       .where('userId', '==', userId)
//       .where('status', '==', 'completed')
//       .orderBy('createdAt', 'desc');

//     const unsubscribe = query.onSnapshot(
//       async snapshot => {
//         const issues = snapshot.docs.map(doc => ({
//           id: doc.id,
//           ...(doc.data() as Omit<IIssue, 'id' | 'categoryStructure'>), // exclude id and categoryStructure for now
//         }));

//         const allCategoryIds = Array.from(
//           new Set(issues.flatMap(issue => issue.categories)),
//         );

//         // Fetch category documents based on all collected IDs
//         const categoryDocs = await firestore()
//           .collection('Categories')
//           .where(firestore.FieldPath.documentId(), 'in', allCategoryIds)
//           .get();

//         const categoryMap = new Map(
//           categoryDocs.docs.map(doc => [doc.id, {id: doc.id, ...doc.data()}]),
//         );

//         // Attach category structure to each issue
//         const issuesWithCategoryStructure: IIssue[] = issues.map(issue => {
//           let parent: any = null;
//           const subCategories: string[] = [];

//           issue.categories.forEach(catId => {
//             const cat = categoryMap.get(catId) as ICategory | undefined;
//             if (!cat) return;
//             if (!cat) return;

//             if (!cat.parentCategory) {
//               // Category is a parent
//               parent = {
//                 title: cat.title,
//                 url: cat.icon.url,
//               };
//             } else {
//               // It's a child/sub-category
//               subCategories.push(cat.title);
//             }
//           });

//           return {
//             ...issue,
//             categoryStructure: {
//               parentCategory: parent,
//               subCategories,
//             },
//           };
//         });

//         setCompletedIssues(issuesWithCategoryStructure);
//         setIssueLoading(false);
//       },
//       err => {
//         console.error('Error fetching completed issues:', err);
//         setIssueLoading(false);
//       },
//     );

//     return () => unsubscribe();
//   }, [userId]);

//   return {
//     pendingIssues,
//     progressIssues,
//     completedIssues,
//     issueLoading,
//   };
// };

// export default useIssues;
import {ICategory, IIssue} from './../interfaces/IIssue';
import {useContext, useEffect, useState} from 'react';
import firestore from '@react-native-firebase/firestore';
import {AuthContext} from '../../App';

const useIssues = () => {
  const {userId} = useContext(AuthContext);

  const [pendingIssues, setPendingIssues] = useState<IIssue[]>([]);
  const [progressIssues, setProgressIssues] = useState<IIssue[]>([]);
  const [completedIssues, setCompletedIssues] = useState<IIssue[]>([]);
  const [issueLoading, setIssueLoading] = useState(true);

  useEffect(() => {
    if (!userId) return;

    const fetchIssuesByStatus = (
      status: 'todo' | 'progress' | 'completed',
      setState: React.Dispatch<React.SetStateAction<IIssue[]>>,
    ) => {
      return firestore()
        .collection('Issues')
        .where('userId', '==', userId)
        .where('status', '==', status)
        .orderBy('createdAt', 'desc')
        .onSnapshot(
          async snapshot => {
            const issues = snapshot.docs.map(doc => ({
              id: doc.id,
              ...(doc.data() as Omit<IIssue, 'id' | 'categoryStructure'>),
            }));

            const allCategoryIds = Array.from(
              new Set(issues.flatMap(issue => issue.categories)),
            );

            let categoryMap = new Map<string, ICategory>();

            if (allCategoryIds.length > 0) {
              const categoryDocs = await firestore()
                .collection('Categories')
                .where(firestore.FieldPath.documentId(), 'in', allCategoryIds)
                .get();

              categoryMap = new Map(
                categoryDocs.docs.map(doc => {
                  const {id, ...data} = doc.data() as ICategory;
                  return [doc.id, {id: doc.id, ...data}];
                }),
              );
            }

            const issuesWithCategoryStructure: IIssue[] = issues.map(issue => {
              let parent: any = null;
              const subCategories: string[] = [];

              issue.categories.forEach(catId => {
                const cat = categoryMap.get(catId);
                if (!cat) return;

                if (!cat.parentCategory) {
                  parent = {
                    title: cat.title,
                    url: cat.icon.url,
                  };
                } else {
                  subCategories.push(cat.title);
                }
              });

              return {
                ...issue,
                categoryStructure: {
                  parentCategory: parent,
                  subCategories,
                },
              };
            });

            setState(issuesWithCategoryStructure);
            setIssueLoading(false);
          },
          err => {
            console.error(`Error fetching ${status} issues:`, err);
            setIssueLoading(false);
          },
        );
    };

    const unsubPending = fetchIssuesByStatus('todo', setPendingIssues);
    const unsubProgress = fetchIssuesByStatus('progress', setProgressIssues);
    const unsubCompleted = fetchIssuesByStatus('completed', setCompletedIssues);

    return () => {
      unsubPending();
      unsubProgress();
      unsubCompleted();
    };
  }, [userId]);

  return {
    pendingIssues,
    progressIssues,
    completedIssues,
    issueLoading,
  };
};

export default useIssues;
