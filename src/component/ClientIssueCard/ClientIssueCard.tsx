import React, {useEffect, useMemo, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import {appStyles, Colors, Fonts} from '../../utilities/theme/theme';
import {
  AttachmentStroke,
  Clock,
  Communication,
  SavedStroke,
  VerticalDot,
} from '../../assets/svgIcons';
import ProgressBar from '../common/ProgressBar/ProgressBar';
import {firebase} from '@react-native-firebase/firestore';
import {getUserById} from '../../helpers/getUserById';
import {IUser} from '../../interfaces/IUser';
import {ITask} from '../../interfaces/ITask';

type TaskCardProps = {
  timeLeft: string;
  priority: string;
  status: string;
  title: string;
  commentsCount: number;
  attachmentsCount: number;
  time: string;
  assignedStaff: string[];
  issueId: string;
  onPress: () => void;
};

const ClientIssueCard: React.FC<TaskCardProps> = ({
  timeLeft,
  priority,
  status,
  title,
  commentsCount,
  attachmentsCount,
  time,
  assignedStaff,
  issueId,
  onPress,
}) => {
  const [avatars, setAvatars] = useState<string[]>([]);
  const [tasks, setTasks] = useState<ITask[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchAvatars = async () => {
      if (!assignedStaff || assignedStaff.length === 0) {
        setAvatars([]);
        return;
      }

      setLoading(true);
      try {
        // Avoid unnecessary await inside map
        const fetchUser = (id: string) => getUserById(id);

        const users: (IUser | null)[] = await Promise.all(
          assignedStaff.map(fetchUser),
        );

        const avatarUrls: string[] = users
          .map(user => user?.profileImage?.url)
          .filter(Boolean) as string[];

        setAvatars(avatarUrls);
      } catch (error) {
        console.error('Error fetching avatars:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAvatars();
  }, [assignedStaff]);

  useEffect(() => {
    if (!issueId) return;
    const unsubscribe = firebase
      .firestore()
      .collection('Tasks')
      .where('issueId', '==', issueId)
      .onSnapshot(
        snapshot => {
          const updatedTasks = snapshot.docs.map(
            doc =>
              ({
                id: doc.id,
                ...doc.data(),
              } as ITask),
          );
          setTasks(updatedTasks);
        },
        error => {
          console.error('Error fetching tasks:', error);
        },
      );

    return () => unsubscribe(); // Clean up on unmount
  }, [issueId]);

  //  Calculate the Progress
  const completeTasks = useMemo(
    () => tasks.filter(task => task.status === 'completed'),
    [tasks],
  );

  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      {/* Top section with labels and icons */}
      <View style={[appStyles.flexSpaceBetween, styles.topRow]}>
        <View style={[appStyles.flexRow, styles.labelGroup]}>
          {timeLeft ? (
            <View style={[styles.chipButton, styles.red]}>
              <Text style={[styles.label]}>{timeLeft}</Text>
            </View>
          ) : null}
          {priority ? (
            <View style={[styles.chipButton, styles.brown]}>
              <Text style={[styles.label]}>{priority}</Text>
            </View>
          ) : null}
          {status ? (
            <View style={[styles.chipButton, styles.yellow]}>
              <Text
                style={[
                  styles.label,
                  {color: '#080C10', textTransform: 'capitalize'},
                ]}>
                {status}
              </Text>
            </View>
          ) : null}
        </View>
        <View style={[appStyles.flexRow, styles.iconGroup]}>
          {/* <TouchableOpacity hitSlop={8} style={styles.menuIcon}>
            <VerticalDot />
          </TouchableOpacity> */}
          <TouchableOpacity hitSlop={8} style={styles.menuIcon}>
            <SavedStroke />
          </TouchableOpacity>
        </View>
      </View>

      {/* Title */}
      <Text numberOfLines={1} style={styles.title}>
        {title}
      </Text>

      {/* Progress bar */}
      <View style={[appStyles.flexSpaceBetween, {marginTop: 12}]}>
        <Text style={styles.progressText}>Progress</Text>
        <Text style={styles.text}>
          {tasks.length > 0
            ? `${Math.round((completeTasks.length / tasks.length) * 100)}%`
            : '0%'}
        </Text>
      </View>

      <ProgressBar
        max={tasks.length > 0 ? tasks.length : 1} // avoid divide by 0
        value={tasks.length > 0 ? completeTasks.length : 0} // show 0 progress if no tasks
        containerStyle={{marginTop: 4}}
        filledStyle={styles.filledStyle}
        unfilledStyle={styles.unfilledStyle}
        showBottomText={false}
      />

      {/* Bottom row with avatars and meta info */}
      <View style={styles.footerRow}>
        {loading ? (
          <ActivityIndicator size="small" color={Colors.white} />
        ) : (
          <View style={styles.avatarGroup}>
            {avatars.map((avatar, index) => (
              <Image key={index} source={{uri: avatar}} style={styles.avatar} />
            ))}
          </View>
        )}
        <View style={styles.metaRow}>
          {/* {commentsCount > 0 && (
            <TouchableOpacity style={styles.metaItem}>
              <Communication />
              <Text style={styles.metaText}>{commentsCount}</Text>
            </TouchableOpacity>
          )} */}
          {attachmentsCount > 0 && (
            <TouchableOpacity style={styles.metaItem}>
              <AttachmentStroke />
              <Text style={styles.metaText}>{attachmentsCount}</Text>
            </TouchableOpacity>
          )}
          <TouchableOpacity style={styles.metaItem}>
            <Clock />
            <Text style={styles.metaText}>{time}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default ClientIssueCard;

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.buttonbgcolor,
    borderRadius: 16,
    padding: 12,
    width: Dimensions.get('screen').width - 70,
  },
  topRow: {
    gap: 26,
  },
  labelGroup: {
    gap: 6,
  },
  iconGroup: {
    gap: 16,
  },
  label: {
    color: Colors.white,
    fontSize: 8,
    fontFamily: Fonts.Medium,
  },
  chipButton: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  red: {
    backgroundColor: '#B62023',
  },
  brown: {
    backgroundColor: '#6E5043',
  },
  yellow: {
    backgroundColor: '#F5ED57',
    color: '#080C10',
  },
  menuIcon: {},
  title: {
    color: Colors.white,
    fontSize: 16,
    fontFamily: Fonts.Bold,
    paddingTop: 12,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.white,
    opacity: 0.3,
    marginTop: 16,
    marginBottom: 12,
  },
  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },
  avatarGroup: {
    flexDirection: 'row',
  },
  avatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: Colors.white,
    marginRight: -7,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    color: '#4B5563',
    fontSize: 10,
    fontFamily: Fonts.Medium,
    paddingTop: 2,
  },
  text: {
    fontSize: 10,
    color: Colors.white,
    fontFamily: Fonts.Semibold,
  },
  progressText: {
    fontSize: 8,
    color: Colors.white,
    fontFamily: Fonts.Medium,
  },
  filledStyle: {
    backgroundColor: '#48D25E',
    height: 6,
    borderRadius: 0,
  },
  unfilledStyle: {
    backgroundColor: Colors.white,
    height: 6,
    borderRadius: 0,
  },
});
