import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  TextInput,
  View,
  TextInputProps,
  ViewStyle,
  TouchableOpacity,
} from 'react-native';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {FC} from 'react';
import {FilterIcon} from '../../assets/svgIcons';

interface Props extends TextInputProps {
  placeholder: string;
  RightIcon: ImageSourcePropType;
  containerStyle?: ViewStyle;
  showSearchFilter?: boolean;
  onPressFilter?: () => void;
}

const Searchbar: FC<Props> = ({
  placeholder,
  RightIcon,
  containerStyle,
  showSearchFilter = false,
  onPressFilter,
  ...rest
}) => {
  return (
    <View style={[styles.wrapper, containerStyle]}>
      <View style={[styles.container]}>
        <TextInput
          style={[styles.textinput]}
          textAlignVertical="center"
          placeholder={placeholder}
          placeholderTextColor={'#7B7B7B'}
          {...rest}
        />
        <Image source={RightIcon} style={styles.RightIconContainer} />
      </View>
      {showSearchFilter ? (
        <TouchableOpacity
          onPress={onPressFilter}
          style={{
            width: 48,
            height: 48,
            borderRadius: 12,
            backgroundColor: Colors.white,
            alignItems: 'center',
            justifyContent: 'center',
          }}>
          <FilterIcon />
        </TouchableOpacity>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  container: {
    height: 48,
    backgroundColor: Colors.white,
    alignItems: 'center',
    flexDirection: 'row',
    paddingHorizontal: 12,
    borderRadius: 12,
    flex: 1,
  },
  RightIconContainer: {
    width: 18,
    height: 18,
  },
  textinput: {
    fontSize: 14,
    color: Colors.primary,
    fontFamily: Fonts.Regular,
    flex: 1,
  },
});

export default Searchbar;
