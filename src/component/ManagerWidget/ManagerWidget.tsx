import React, {useCallback, useContext, useState} from 'react';
import {
  StyleSheet,
  Text,
  View,
  Image,
  Dimensions,
  TouchableOpacity,
  GestureResponderEvent,
  ActivityIndicator,
} from 'react-native';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {useFocusEffect} from '@react-navigation/native';
import {AuthContext} from '../../../App';
import {getIssuesByManagerId} from '../../backend/Issues';
import {IIssue} from '../../interfaces/IIssue';
import {IUser} from '../../interfaces/IUser';
import {getUserById} from '../../helpers/getUserById';

interface Props {
  icon: React.ReactNode;
  title: string;
  onPress?: (event: GestureResponderEvent) => void;
  status?: string;
}

const ManagerWidget: React.FC<Props> = ({icon, title, onPress, status}) => {
  const {userId} = useContext(AuthContext);
  const [issues, setIssues] = useState<IIssue[]>([]);
  const [loading, setIsLoading] = useState(true);
  const [avatars, setAvatars] = useState<string[]>([]);

  const fetchAvatars = async (assignedStaff: string[]) => {
    if (assignedStaff && assignedStaff.length > 0) {
      const avatarUrls: string[] = await Promise.all(
        assignedStaff.map(async (id: string): Promise<string> => {
          const user: IUser | null = await getUserById(id);
          return user?.profileImage?.url ?? '';
        }),
      );
      setAvatars(avatarUrls.filter(Boolean));
    } else {
      setAvatars([]);
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (!status) return;

      let isActive = true;

      const fetch = async () => {
        const issues = await getIssuesByManagerId(userId, status); // Add `limit` if needed
        if (isActive) {
          fetchAvatars(
            Array.from(new Set(issues.flatMap(issue => issue.assignedStaff))),
          );
          setIssues(issues);
          setIsLoading(false);
        }
      };

      fetch();

      return () => {
        isActive = false;
      };
    }, [userId, status]), // Include all dependencies
  );

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      {loading ? (
        <ActivityIndicator
          style={{alignSelf: 'center'}}
          color={Colors.buttonbgcolor}
          size={'small'}
        />
      ) : (
        <View style={styles.contentContainer}>
          {icon}
          <Text style={styles.title}>{title}</Text>
          {avatars.length ? (
            <>
              <View style={styles.avatarGroup}>
                {avatars?.slice(0, 16).map((avatar, index) => (
                  <Image
                    key={index}
                    source={{uri: avatar}}
                    style={styles.avatar}
                  />
                ))}
              </View>
              <Text style={styles.taskInfo}>
                <Text style={styles.taskCount}>{issues.length}</Text>{' '}
                {issues.length === 1 ? 'Issue' : 'Issues'} found
              </Text>
            </>
          ) : (
            <>
              <Text style={styles.taskInfo}>
                <Text style={styles.taskCount}>{issues.length}</Text>{' '}
                {issues.length === 1 ? 'Issue' : 'Issues'} found
              </Text>
              <View style={styles.avatarGroup} />
            </>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

export default ManagerWidget;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    paddingHorizontal: 12,
    // paddingVertical: 16,
    borderRadius: 12,
    width: Dimensions.get('screen').width / 2 - 30,
    height: 132,
    justifyContent: 'center',
  },
  title: {
    fontSize: 12,
    fontFamily: Fonts.Medium,
    color: Colors.primary,
    marginTop: 10,
  },
  avatarGroup: {
    flexDirection: 'row',
    marginTop: 8,
    height: 20,
    alignItems: 'center',
  },
  avatar: {
    width: 20,
    height: 20,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.white,
    marginRight: -6,
  },
  taskInfo: {
    fontSize: 10,
    color: Colors.lightcolor,
    fontFamily: Fonts.Medium,
    paddingTop: 6,
  },
  taskCount: {
    color: Colors.primary,
    fontSize: 14,
    fontFamily: Fonts.Semibold,
  },
});
