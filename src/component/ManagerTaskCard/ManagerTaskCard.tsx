import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import ProgressBar from '../common/ProgressBar/ProgressBar';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {getColorByStatus} from '../../helpers/getColorByStatus';
import {Timestamp} from '@react-native-firebase/firestore';
import UserCard from '../common/UserCard/UserCard';
import {getUserById} from '../../helpers/getUserById';
import {IUser} from '../../interfaces/IUser';

// Props interface for the card
interface ManagerIssueCardProps {
  timeLeft: string;
  priority: string;
  status: string;
  title: string;
  note: string;
  dueDate: Timestamp;
  containerStyle?: ViewStyle;
  onPressManagerIssueCard?: (staffUser?: IUser | null) => void;
  avatars?: string[];
  progress?: number;
  staffId: string;
}

const ManagerTaskCard: React.FC<ManagerIssueCardProps> = ({
  timeLeft,
  priority,
  status,
  title,
  note,
  dueDate,
  containerStyle,
  onPressManagerIssueCard,
  staffId,
}) => {
  const [loading, setLoading] = useState(true);
  const [staffUser, setStaffUser] = useState<IUser | null>(null);

  useEffect(() => {
    const fetch = async () => {
      if (staffId) {
        const userData = await getUserById(staffId);
        setStaffUser(userData);
      }
      setLoading(false);
    };

    fetch();
  }, [staffId]);

  return (
    <TouchableOpacity
      onPress={() =>
        onPressManagerIssueCard && onPressManagerIssueCard(staffUser || null)
      }
      style={[styles.card, containerStyle]}>
      {/* Labels Row: Time Left, Priority, Status */}
      <View style={styles.labelsRow}>
        <View style={[styles.label, {backgroundColor: '#D21F3C'}]}>
          <Text style={[styles.labelText, {color: '#fff'}]}>{timeLeft}</Text>
        </View>
        <View style={[styles.label, {backgroundColor: '#19211E'}]}>
          <Text style={[styles.labelText, {color: '#fff'}]}>{priority}</Text>
        </View>
        <View
          style={[
            styles.label,
            {backgroundColor: getColorByStatus(status as any) as any},
          ]}>
          <Text style={[styles.labelText, {color: Colors.white}]}>
            {status}
          </Text>
        </View>
      </View>

      {/* Task Title */}
      <Text numberOfLines={1} style={styles.title}>
        {title}
      </Text>

      {/* Task Description (Single Line) */}
      <Text style={styles.description}>{note}</Text>
      {loading ? (
        <ActivityIndicator style={{marginTop: 16}} />
      ) : (
        <UserCard
          date={dueDate}
          name={staffUser?.name || ''}
          email={staffUser?.email}
          imageUrl={staffUser?.profileImage?.url || ''}
          containerStyle={{marginTop: 6}}
        />
      )}

      {/* Footer Row: Avatars + Due Date */}
      {/* <View style={styles.footerRow}>
        <View style={styles.avatarGroup}>
          {avatars?.map((avatar, index) => (
            <Image key={index} source={{uri: avatar}} style={styles.avatar} />
          ))}
        </View>
        <Text style={styles.date}>{dueDate}</Text>
      </View> */}

      {/* Progress Bar + Percentage */}
      {/* <View style={styles.progressRow}>
        <ProgressBar
          containerStyle={{width: '91%'}}
          max={100}
          value={progress}
          showBottomText={false}
          filledStyle={{height: 4}}
          unfilledStyle={{height: 4}}
        />
        <Text style={styles.percentText}>{Math.round(progress)}%</Text>
      </View> */}
    </TouchableOpacity>
  );
};

export default ManagerTaskCard;

// Styles
const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 12,
  },
  labelsRow: {
    flexDirection: 'row',
    gap: 9,
    alignItems: 'center',
  },
  label: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
  },
  labelText: {
    color: 'white',
    fontFamily: Fonts.Medium,
    fontSize: 10,
    textTransform: 'capitalize',
  },
  title: {
    fontSize: 14,
    fontFamily: Fonts.Semibold,
    color: '#080C10',
    marginTop: 8,
  },
  description: {
    color: '#4B5563',
    fontSize: 10,
    fontFamily: Fonts.Medium,
    paddingTop: 2,
  },
  date: {
    fontFamily: Fonts.Semibold,
    color: '#4B5563',
    fontSize: 10,
  },
  progressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 13,
  },
  percentText: {
    color: Colors.primary,
    fontFamily: Fonts.Semibold,
    fontSize: 10,
  },
  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  avatarGroup: {
    flexDirection: 'row',
  },
  avatar: {
    width: 26,
    height: 26,
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: Colors.white,
    marginRight: -7,
  },
});
