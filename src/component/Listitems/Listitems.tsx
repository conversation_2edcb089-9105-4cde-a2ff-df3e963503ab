import React from 'react';
import {
  Text,
  StyleSheet,
  View,
  Image,
  TouchableOpacity,
  TextInputProps,
  ImageSourcePropType,
  ViewStyle,
  TextStyle,
} from 'react-native';
import {images} from '../../assets/images';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {FC} from 'react';

interface Props extends TextInputProps {
  title?: string;
  leftIcon?: ImageSourcePropType;
  RightIcon?: ImageSourcePropType;
  containerStyle?: ViewStyle;
  textContainer?: TextStyle;
  onPress?: () => void;
}

const Listitems: FC<Props> = ({
  leftIcon,
  title,
  RightIcon,
  containerStyle,
  textContainer,
  onPress,
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.container, containerStyle]}>
      <Image source={leftIcon} style={styles.lefticon} resizeMode="contain" />
      <View style={styles.innerContainer}>
        <Text style={[styles.textStyle, textContainer]}>{title}</Text>
        <Image
          source={RightIcon}
          style={styles.Righticon}
          resizeMode="contain"
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 48,
    borderRadius: 12,
    backgroundColor: Colors.white,
    flexDirection: 'row',
    paddingHorizontal: 12,
    paddingVertical: 14,
    marginTop: 16,
  },
  innerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
  },
  lefticon: {
    width: 20,
    height: 20,
    marginRight: 12,
  },
  Righticon: {
    width: 20,
    height: 20,
  },
  textStyle: {
    fontSize: 14,
    color: Colors.primary,
    fontFamily: Fonts.Regular,
  },
});

export default Listitems;
