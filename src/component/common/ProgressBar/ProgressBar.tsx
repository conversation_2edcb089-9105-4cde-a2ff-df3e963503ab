import React, {useEffect, useRef} from 'react';
import {
  View,
  Animated,
  StyleSheet,
  Text,
  ViewStyle,
  TextStyle,
} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
interface ProgressBarProps {
  value: number; // Current step
  max: number; // Total number of steps
  containerStyle?: ViewStyle;
  filledStyle?: ViewStyle;
  unfilledStyle?: ViewStyle;
  textStyle?: TextStyle;
  showBottomText?: boolean;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  max,
  containerStyle,
  filledStyle,
  unfilledStyle,
  textStyle,
  showBottomText = true,
}) => {
  const progressAnim = useRef(new Animated.Value(0)).current;

  const progressRatio = Math.min(value / max, 1);

  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: progressRatio,
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [progressRatio]);

  const widthInterpolate = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <View style={[styles.wrapper, containerStyle]}>
      <View style={[styles.unfilledBar, unfilledStyle]}>
        <Animated.View
          style={[styles.filledBar, filledStyle, {width: widthInterpolate}]}
        />
      </View>
      {showBottomText ? (
        <Text style={[styles.stepText, textStyle]}>
          Step {Math.min(value, max)} of {max}
        </Text>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
  },
  unfilledBar: {
    height: 7,
    width: '100%',
    backgroundColor: '#EAEAEA',
    borderRadius: 100,
    overflow: 'hidden',
  },
  filledBar: {
    height: 7,
    backgroundColor: Colors.buttonbgcolor,
    borderRadius: 100,
  },
  stepText: {
    marginTop: 10,
    fontSize: 12,
    color: '#03020A',
    textAlign: 'right',
    fontFamily: Fonts.Medium,
  },
});

export default ProgressBar;
