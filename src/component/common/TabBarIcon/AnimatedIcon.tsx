import React, {useEffect} from 'react';
import {Image, ImageSourcePropType, StyleSheet} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  Easing,
} from 'react-native-reanimated';

type AnimatedIconProps = {
  activeIcon: ImageSourcePropType;
  inActiveIcon: ImageSourcePropType;
  focused: boolean;
};

const AnimatedIcon: React.FC<AnimatedIconProps> = ({
  activeIcon,
  inActiveIcon,
  focused,
}) => {
  const animatedScale = useSharedValue(focused ? 1.3 : 1);

  useEffect(() => {
    animatedScale.value = withTiming(focused ? 1.3 : 1, {
      duration: 250,
      easing: Easing.out(Easing.exp),
    });
  }, [focused]);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{scale: animatedScale.value}],
  }));

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      <Image
        source={focused ? activeIcon : inActiveIcon}
        style={styles.icon}
        resizeMode="contain"
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  icon: {
    width: 24,
    height: 24,
  },
});

export default AnimatedIcon;
