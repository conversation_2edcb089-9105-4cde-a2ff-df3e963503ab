import {
  Image,
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import AppIntroSlider from 'react-native-app-intro-slider';
import {Colors} from '../../../utilities/theme/theme';
import ImageView from 'react-native-image-viewing';
interface Props {
  images: string[];
}
const AppSlider: React.FC<Props> = ({images}) => {
  const [visible, setIsVisible] = useState(false);
  const [index, setIndex] = useState(0);
  const renderItem = ({item, index}: {item: string; index: number}) => {
    return (
      <TouchableOpacity
        activeOpacity={1}
        style={styles.innerContainer}
        onPress={() => {
          setIsVisible(true);
          setIndex(index);
        }}>
        <Image
          style={styles.sliderImageContainer}
          source={{
            uri: item,
          }}
          resizeMode="cover"
        />
      </TouchableOpacity>
    );
  };
  return (
    <View style={styles.container}>
      <AppIntroSlider
        data={images || []}
        renderItem={renderItem}
        dotClickEnabled
        activeDotStyle={styles.activeDot}
        dotStyle={styles.inActiveDot}
        showNextButton={false}
        showDoneButton={false}
      />
      <ImageView
        images={images.map(img => ({uri: img}))}
        imageIndex={index}
        visible={visible}
        onRequestClose={() => setIsVisible(false)}
      />
    </View>
  );
};

export default AppSlider;

const styles = StyleSheet.create({
  container: {
    height: 200,
    borderRadius: 8,
    marginTop: 16,
    overflow: 'hidden',
  },
  hitSlop: {
    left: 10,
    right: 10,
    bottom: 10,
    top: 10,
  },
  rowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  innerContainer: {
    height: 220,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.white,
    borderRadius: 8,
    overflow: 'hidden',
  },

  sliderImageContainer: {
    width: '100%',
    height: 220,
    borderRadius: 8,
    overflow: 'hidden',
  },

  activeDot: {
    width: 24,
    height: 8,
    backgroundColor: Colors.buttonbgcolor,
    borderRadius: 8 / 2,
    marginTop: 40,
  },
  inActiveDot: {
    width: 8,
    height: 8,
    backgroundColor: Colors.borderColor,
    borderRadius: 8 / 2,
    marginLeft: 6,
    marginTop: 40,
  },
});
