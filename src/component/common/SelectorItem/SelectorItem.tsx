import React from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import {ChevronDown} from '../../../assets/svgIcons';
import {Colors, Fonts} from '../../../utilities/theme/theme';

interface SelectorItemProps {
  title: string;
  icon: React.ReactNode;
  onPress: () => void;
  label: string;
  color?: string;
  containerStyle?: ViewStyle;
}

const SelectorItem: React.FC<SelectorItemProps> = ({
  title,
  icon,
  onPress,
  label,
  color,
  containerStyle,
}) => {
  return (
    <View style={containerStyle}>
      <Text style={styles.label}>{label}</Text>
      <TouchableOpacity style={[styles.container]} onPress={onPress}>
        <View style={styles.iconWrapper}>{icon}</View>
        <Text numberOfLines={1} style={[styles.text, {color}]}>
          {title}
        </Text>
        <ChevronDown />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#98A2B3',
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.white,
    height: 45,
    paddingHorizontal: 12,
  },
  iconWrapper: {
    marginRight: 12,
  },
  text: {
    flex: 1,
    fontSize: 14,
    color: Colors.primary_text,
    fontFamily: Fonts.Regular,
    textTransform: 'capitalize',
  },
  label: {
    fontSize: 10,
    color: '#475467',
    fontFamily: Fonts.Regular,
    marginBottom: 6,
  },
});

export default SelectorItem;
