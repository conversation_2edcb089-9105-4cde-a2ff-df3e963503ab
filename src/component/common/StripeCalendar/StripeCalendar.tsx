import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import CalendarStrip from 'react-native-calendar-strip';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {s, vs, ms} from 'react-native-size-matters';
import moment from 'moment';

interface Props {
  selectedDate: Date;
  onDateSelected: (date: Date) => void;
}

const StripeCalendar: React.FC<Props> = ({selectedDate, onDateSelected}) => {
  return (
    <View style={{marginTop: 30}}>
      <CalendarStrip
        onDateSelected={onDateSelected || moment()}
        selectedDate={selectedDate}
        useIsoWeekday={false}
        scrollable
        scrollToOnSetSelectedDate
        style={styles.calendarStrip}
        dateNameStyle={styles.dateName}
        dateNumberStyle={styles.dateNumber}
        dayContainerStyle={styles.dayContainer}
        maxDayComponentSize={80}
        minDayComponentSize={65}
        dayComponentHeight={86}
        highlightDateContainerStyle={styles.highlightDateContainer}
        highlightDateNumberStyle={styles.highlightDateNumber}
        highlightDateNameStyle={styles.highlightDateName}
        iconLeftStyle={styles.calendarLeftIcon}
        iconRightStyle={styles.calendarRightIcon}
        calendarHeaderContainerStyle={styles.calendarHeaderContainer}
        calendarHeaderStyle={styles.calendarHeader}
        upperCaseDays={false}
        calendarHeaderFormat={'MMMM'}
      />
    </View>
  );
};

export default StripeCalendar;

const styles = StyleSheet.create({
  calendarStrip: {
    height: ms(140),
    backgroundColor: Colors.background,
  },
  dayContainer: {
    backgroundColor: Colors.white,
    marginLeft: s(15),
    height: ms(69),
    width: ms(45),
    borderRadius: 12,
  },
  dateName: {
    fontSize: ms(9.5),
    color: Colors.primary,
    fontFamily: Fonts.Medium,
  },
  dateNumber: {
    fontSize: ms(16),
    color: Colors.primary,
    lineHeight: 22,
    fontFamily: Fonts.Semibold,
    marginTop: vs(4),
  },
  highlightDateContainer: {
    backgroundColor: Colors.buttonbgcolor,
    borderRadius: 12,
  },
  highlightDateNumber: {
    color: Colors.white,
  },
  highlightDateName: {
    color: Colors.white,
  },
  calendarRightIcon: {
    position: 'absolute',
    top: -90,
    right: -1000,
    tintColor: Colors.background,
  },
  calendarLeftIcon: {
    position: 'absolute',
    top: -90,
    right: -1000,
    tintColor: Colors.background,
  },
  calendarHeaderContainer: {
    textAlign: 'left',
    alignSelf: 'flex-start',
    marginLeft: s(20),
  },
  calendarHeader: {
    fontSize: ms(16),
    fontFamily: Fonts.Semibold,
    lineHeight: 27,
    color: Colors.primary,
  },
});
