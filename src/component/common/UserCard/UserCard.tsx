import {Image, StyleSheet, Text, View, ViewStyle} from 'react-native';
import React from 'react';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import moment from 'moment';
import {Timestamp} from '@react-native-firebase/firestore';

interface Props {
  imageUrl: string;
  name: string;
  date?: Timestamp;
  userRole?: string;
  email?: string;
  containerStyle?: ViewStyle;
}

const UserCard: React.FC<Props> = ({
  imageUrl,
  name,
  date,
  userRole,
  email,
  containerStyle,
}) => {
  return (
    <View style={[styles.userRow, containerStyle]}>
      <Image
        style={styles.avatar}
        source={{
          uri: imageUrl || 'https://randomuser.me/api/portraits/men/1.jpg',
        }}
      />
      <View style={styles.userInfo}>
        <Text style={styles.userName}>{name}</Text>
        <View style={styles.userMetaRow}>
          {userRole ? (
            <Text style={styles.userRole}>{userRole}</Text>
          ) : (
            <Text style={styles.userRole}>{email}</Text>
          )}
          <Text style={styles.dateText}>
            {moment(date.seconds * 1000).format('DD MMM YYYY h:mm A')}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default UserCard;

const styles = StyleSheet.create({
  userRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
    gap: 8,
  },
  avatar: {
    width: 30,
    height: 30,
    borderRadius: 30,
    backgroundColor: Colors.buttonbgcolor,
  },
  userInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  userName: {
    fontSize: 10,
    color: Colors.primary_text,
    fontFamily: Fonts.Medium,
  },
  userMetaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  userRole: {
    fontSize: 10,
    color: Colors.buttonbgcolor,
    fontFamily: Fonts.Medium,
  },
  dateText: {
    fontSize: 11,
    color: Colors.lightcolor,
    fontFamily: Fonts.Medium,
  },
});
