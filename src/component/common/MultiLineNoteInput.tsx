import React from 'react';
import {
  View,
  TextInput,
  TextInputProps,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import {Colors, Fonts} from '../../utilities/theme/theme';

interface MultiLineNoteInputProps extends TextInputProps {
  label?: string;
  containerStyle?: ViewStyle;
  labelStyles?: TextStyle;
}

const MultiLineNoteInput: React.FC<MultiLineNoteInputProps> = ({
  label = 'Notes',
  containerStyle,
  style,
  labelStyles,
  ...rest
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      {label && <Text style={[styles.label, labelStyles]}>{label}</Text>}
      <TextInput
        multiline
        style={[styles.textInput, style]}
        textAlignVertical="top"
        {...rest}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  label: {
    marginBottom: 6,
    fontSize: 14,
    color: '#555',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#98A2B3',
    borderRadius: 10,
    paddingHorizontal: 12,
    fontSize: 12,
    height: 100,
    paddingVertical: 10,
    color: Colors.primary,
    fontFamily: Fonts.Regular,
  },
});

export default MultiLineNoteInput;
