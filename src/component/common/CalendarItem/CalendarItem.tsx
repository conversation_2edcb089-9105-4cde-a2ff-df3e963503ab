import {
  ActivityIndicator,
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {getUserById} from '../../../helpers/getUserById';
import {IUser} from '../../../interfaces/IUser';

type CalendarItemProps = {
  title: string;
  taskDueDate: string;
  staffId: string;
  onPressCalendarItem?: (staffUser?: IUser | null) => void;
  containerStyle?: ViewStyle;
};

const CalendarItem: React.FC<CalendarItemProps> = ({
  title,
  taskDueDate,
  staffId,
  onPressCalendarItem,
  containerStyle,
}) => {
  const [loading, setLoading] = useState(true);
  const [staffUser, setStaffUser] = useState<IUser | null>(null);

  useEffect(() => {
    const fetch = async () => {
      if (staffId) {
        const userData = await getUserById(staffId);
        setStaffUser(userData);
      }
      setLoading(false);
    };

    fetch();
  }, [staffId]);

  return (
    <TouchableOpacity
      onPress={() =>
        onPressCalendarItem && onPressCalendarItem(staffUser || null)
      }
      activeOpacity={0.5}
      style={[styles.itemContainer, containerStyle]}>
      <View style={styles.verticalBar} />
      <View style={styles.itemRowContainer}>
        <View>
          <Text style={styles.title}>{title}</Text>
          <Text style={styles.time}>Deadline: {taskDueDate}</Text>
        </View>
        <View style={styles.imageContainer}>
          {loading ? (
            <ActivityIndicator color={Colors.buttonbgcolor} size={'small'} />
          ) : (
            <Image
              style={{width: '100%', height: '100%', borderRadius: 40}}
              source={{
                uri: staffUser?.profileImage?.url,
              }}
            />
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default CalendarItem;

const styles = StyleSheet.create({
  itemContainer: {
    marginHorizontal: 20,
    borderRadius: 12,
    backgroundColor: Colors.white,
    height: 60,
    flexDirection: 'row',
    gap: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    marginBottom: 12,
  },
  verticalBar: {
    backgroundColor: Colors.buttonbgcolor,
    height: 60,
    width: 16,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
  },
  itemRowContainer: {
    flex: 1,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 14,
    color: Colors.buttonbgcolor,
    fontFamily: Fonts.Medium,
  },
  time: {
    fontSize: 12,
    color: Colors.buttonbgcolor,
    fontFamily: Fonts.Regular,
    paddingTop: 3,
  },
  imageContainer: {
    width: 40,
    height: 40,
    borderRadius: 40 / 2,
    borderWidth: 1,
    borderColor: Colors.white,
    marginRight: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
