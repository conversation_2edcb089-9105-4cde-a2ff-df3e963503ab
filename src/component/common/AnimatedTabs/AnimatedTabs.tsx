import {
  Dimensions,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Animated,
} from 'react-native';
import React, {useRef, useEffect} from 'react';
import {Colors, Fonts} from '../../../utilities/theme/theme';

interface Props {
  activeTab: string;
  setActiveTab: (val: 'Pending' | 'In Progress' | 'Completed') => void;
  tabs: string[];
  counters: {
    Pending: number;
    In_Progress: number;
    Completed: number;
  };
}

const AnimatedTabs: React.FC<Props> = ({
  activeTab,
  setActiveTab,
  tabs,
  counters,
}) => {
  const screenWidth = Dimensions.get('screen').width;
  const horizontalPadding = 26;
  const tabCount = tabs.length;

  const tabWidth = (screenWidth - 2 * horizontalPadding) / tabCount;

  const translateX = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const targetIndex = tabs.indexOf(activeTab);
    Animated.timing(translateX, {
      toValue: targetIndex * tabWidth,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [activeTab, tabWidth, tabs]);

  return (
    <View style={[styles.tabsContainer, {paddingHorizontal: 0}]}>
      {/* Animated sliding indicator */}
      <Animated.View
        style={[
          styles.indicator,
          {
            width: tabWidth,
            transform: [{translateX}],
            left: activeTab === 'Pending' ? 4 : 0,
          },
        ]}
      />
      {tabs.map(tab => (
        <TouchableOpacity
          key={tab}
          style={[styles.tabContainer, {width: tabWidth}]}
          onPress={() => setActiveTab(tab)}>
          <Text
            style={[
              styles.tabText,
              {color: activeTab === tab ? Colors.white : '#475467'},
            ]}>
            {tab}
          </Text>
          {counters ? (
            <View
              style={[
                styles.circleIndicator,
                {backgroundColor: activeTab === tab ? '#F95555' : '#D0D5DD'},
              ]}>
              <Text
                numberOfLines={1}
                style={[
                  styles.tabText,
                  {
                    color: activeTab === tab ? Colors.white : '#475467',
                    fontSize: Dimensions.get('screen').width / 42,
                  },
                ]}>
                {tab === 'Pending'
                  ? counters.Pending
                  : tab === 'In Progress'
                  ? counters.In_Progress
                  : counters.Completed}
              </Text>
            </View>
          ) : null}
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default AnimatedTabs;

const styles = StyleSheet.create({
  tabsContainer: {
    flexDirection: 'row',
    position: 'relative',
    borderRadius: 40,
    paddingVertical: 4,
    backgroundColor: Colors.white,
    marginVertical: 24,
  },
  tabContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 37,
    flexDirection: 'row',
    gap: 4,
  },
  tabText: {
    fontSize: Dimensions.get('screen').width / 32,
    fontFamily: Fonts.Medium,
    // paddingTop: 3,
  },
  circleIndicator: {
    backgroundColor: '#F95555',
    borderRadius: 7.5,
    alignItems: 'center',
    justifyContent: 'center',
    width: 15,
    height: 15,
  },
  indicator: {
    position: 'absolute',
    height: 37,
    backgroundColor: Colors.buttonbgcolor,
    bottom: 3.5,
    borderRadius: 40,
  },
});
