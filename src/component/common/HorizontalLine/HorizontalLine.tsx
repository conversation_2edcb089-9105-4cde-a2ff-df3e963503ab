import React from 'react';
import {View, ViewStyle} from 'react-native';

interface Props {
  containerStyle?: ViewStyle;
  height?: number;
  marginTop?: number;
  marginBottom?: number;
  marginVertical?: number;
  backgroundColor?: string;
}

const HorizontalLine: React.FC<Props> = ({
  containerStyle,
  height = 1,
  marginTop,
  marginBottom,
  marginVertical,
  backgroundColor = '#E2E2E2',
}) => {
  return (
    <View
      style={[
        {
          height,
          backgroundColor,
          marginTop,
          marginBottom,
          marginVertical,
        },
        containerStyle,
      ]}
    />
  );
};

export default HorizontalLine;
