import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Image,
  Dimensions,
} from 'react-native';
import React from 'react';
import {Attachment, CrossRed} from '../../../assets/svgIcons';

const AttachmentComp = ({
  url,
  onPress,
  onRemove,
}: {
  url?: string;
  onPress: () => void;
  onRemove?: () => void;
}) => {
  return (
    <TouchableOpacity
      disabled={!!url}
      style={styles.attachmentBox}
      onPress={onPress}>
      {url ? (
        <>
          <Image source={{uri: url}} style={styles.attachmentImage} />
        </>
      ) : (
        <Attachment />
      )}
      {url ? (
        <TouchableOpacity
          hitSlop={10}
          style={styles.removeButton}
          onPress={onRemove}>
          <CrossRed width={12} height={12} />
        </TouchableOpacity>
      ) : null}
    </TouchableOpacity>
  );
};

export default AttachmentComp;

const BOX_SIZE = Dimensions.get('screen').width / 3 - 34;
const styles = StyleSheet.create({
  attachmentBox: {
    borderWidth: 1,
    borderColor: '#BDB4FE',
    backgroundColor: '#F4F3FF',
    borderRadius: 8,
    width: BOX_SIZE,
    height: BOX_SIZE,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  attachmentImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  removeButton: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#fff',
    borderRadius: 10,
    zIndex: 10,
  },
});
