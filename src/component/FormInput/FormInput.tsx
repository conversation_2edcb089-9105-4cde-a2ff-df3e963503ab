import React from 'react';
import {View, Text, StyleSheet, TextInput, TextInputProps} from 'react-native';
import {Colors, Fonts} from '../../utilities/theme/theme';
interface SelectorInputProps extends TextInputProps {
  label: string;
  icon?: React.ReactNode;
}

const FormInput: React.FC<SelectorInputProps> = ({label, icon, ...props}) => {
  return (
    <View>
      <Text style={styles.label}>{label}</Text>
      <View style={styles.container}>
        {icon && <View style={styles.iconWrapper}>{icon}</View>}
        <TextInput
          style={styles.input}
          placeholderTextColor="#98A2B3"
          {...props}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#98A2B3',
    borderRadius: 12,
    alignItems: 'center',
    backgroundColor: Colors.white,
    height: 45,
    paddingHorizontal: 12,
  },
  iconWrapper: {
    marginRight: 12,
  },
  input: {
    flex: 1,
    fontSize: 14,
    color: Colors.primary_text,
    fontFamily: Fonts.Regular,
    padding: 0, // Remove default iOS padding
  },
  label: {
    fontSize: 10,
    color: '#475467',
    fontFamily: Fonts.Regular,
    marginBottom: 6,
  },
});

export default FormInput;
