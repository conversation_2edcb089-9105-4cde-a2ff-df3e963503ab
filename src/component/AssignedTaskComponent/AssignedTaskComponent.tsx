import {
  ImageBackground,
  Text,
  View,
  TouchableOpacity,
  ViewStyle,
  Image,
} from 'react-native';
import React, {useRef} from 'react';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {images} from '../../assets/images';
import {styles} from './styles';
import {CircularProgressBase} from 'react-native-circular-progress-indicator';
import {ms} from 'react-native-size-matters';
import {ITask} from '../../interfaces/ITask';
import moment from 'moment';

interface props {
  pending?: boolean;
  variant?: string;
  onPress?: () => void;
  item: ITask;
  delayed: boolean;
  containerStyle?: ViewStyle;
  today?: boolean;
}

const AssignedTaskComponent: React.FC<props> = ({
  pending,
  variant,
  onPress,
  item,
  delayed,
  containerStyle,
  today = new Date(),
}) => {
  const isToday = moment(item.date.toDate()).isSame(new Date(), 'day');

  return (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.container,
        containerStyle,
        {backgroundColor: Colors.white},
      ]}
      activeOpacity={0.7}>
      <View
        style={[
          styles.sideBar,
          {
            backgroundColor: delayed ? '#CD5C5C' : Colors.buttonbgcolor,
          },
        ]}
      />
      <View style={styles.contentContainer}>
        <View style={styles.textContainer}>
          <Text
            style={[styles.taskTitle, {color: Colors.buttonbgcolor}]}
            numberOfLines={1}>
            {item.title}
          </Text>
          <Text style={[styles.taskTime, {color: `${Colors.primary}80`}]}>
            Deadline: {moment(item.date.toDate()).format('DD-MMM')} .{' '}
            {moment(item.time.toDate()).format('hh:mm a')}
          </Text>
        </View>
        {variant === 'primary' ? (
          <ImageBackground
            source={images.attachmentIcon}
            style={styles.avatar}
          />
        ) : pending ? (
          <View style={styles.completeContainer}>
            <Text style={styles.CompText}>Completed</Text>
            <Image style={styles.CompImg} source={images.completeicon} />
          </View>
        ) : (
          <View style={styles.completeContainer}>
            <Text style={[styles.CompText, {color: Colors.buttonbgcolor}]}>
              Pending
            </Text>
            <Image
              style={[styles.CompImg, {tintColor: Colors.buttonbgcolor}]}
              source={images.hourglass}
            />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default AssignedTaskComponent;
