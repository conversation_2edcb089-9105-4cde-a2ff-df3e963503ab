import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  GestureResponderEvent,
  ViewStyle,
} from 'react-native';
import {appStyles, Colors, Fonts} from '../../utilities/theme/theme';
import {ArrowRight} from '../../assets/svgIcons';
import {getColorByStatus} from '../../helpers/getColorByStatus';

interface IssueListItemProps {
  status: string;
  title: string;
  note: string;
  date: string;
  onViewDetails: (event: GestureResponderEvent) => void;
  containerStyle?: ViewStyle;
}

const IssueListItem: React.FC<IssueListItemProps> = ({
  status,
  title,
  note,
  date,
  onViewDetails,
  containerStyle,
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <View style={appStyles.flexSpaceBetween}>
        <View
          style={[
            styles.statusContainer,
            {
              backgroundColor: getColorByStatus(status),
            },
          ]}>
          <Text style={[styles.statusText, {color: Colors.white}]}>
            {status}
          </Text>
        </View>
        <TouchableOpacity
          hitSlop={16}
          onPress={onViewDetails}
          style={[appStyles.flexRow, {gap: 6}]}>
          <Text style={styles.detailsText}>View Details</Text>
          <ArrowRight />
        </TouchableOpacity>
      </View>

      <Text style={styles.title}>{title}</Text>

      <Text style={styles.note} numberOfLines={2}>
        {note}
      </Text>
      <Text style={styles.dateText}>{date}</Text>
    </View>
  );
};

export default IssueListItem;
const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    paddingHorizontal: 12,
    borderRadius: 12,
    paddingTop: 16,
    paddingBottom: 6,
  },
  statusContainer: {
    backgroundColor: '#82C91E',
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
  },
  statusText: {
    color: 'white',
    fontFamily: Fonts.Medium,
    fontSize: 12,
    textTransform: 'capitalize',
  },
  title: {
    fontSize: 14,
    fontFamily: Fonts.Semibold,
    color: '#080C10',
    marginTop: 8,
  },
  note: {
    fontSize: 10,
    color: '#4B5563',
    marginTop: 2,
  },

  dateText: {
    fontSize: 12,
    color: '#4B5563',
    fontFamily: Fonts.Semibold,
    paddingTop: 6,
  },
  detailsButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailsText: {
    color: '#0386AC',
    fontSize: 8,
    fontFamily: Fonts.Semibold,
  },
});
