import React, {useState, useContext} from 'react';
import {
  View,
  TextInput,
  Image,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import {Attachments, SendIcon, SendIconActive} from '../../assets/svgIcons';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {AuthContext} from '../../../App';
import {images} from '../../assets/images';

interface Props {
  onSendComment: (message: string) => void;
  currentUserImage?: string;
}

const CommentBox: React.FC<Props> = ({onSendComment, currentUserImage}) => {
  const [text, setText] = useState('');
  const {userData} = useContext(AuthContext);

  const handleSend = () => {
    if (text.trim()) {
      onSendComment(text.trim());
      setText('');
    }
  };
  return (
    <View style={styles.container}>
      <Image
        source={{
          uri:
            currentUserImage ||
            userData?.profileImage?.url ||
            String(images.avatarPlaceholder),
        }}
        style={styles.avatar}
      />
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.input}
          placeholder="Write a comment..."
          placeholderTextColor={'#98A2B3'}
          value={text}
          onChangeText={setText}
        />
        <TouchableOpacity style={styles.iconButton}>
          <Attachments />
        </TouchableOpacity>
        <TouchableOpacity style={styles.iconButton} onPress={handleSend}>
          {text.trim() ? <SendIconActive /> : <SendIcon />}
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default CommentBox;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingBottom: 10,
  },
  commentsContainer: {
    flex: 1,
    marginBottom: 10,
  },
  commentText: {
    marginVertical: 4,
    fontSize: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderColor: '#EAECF0',
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 10,
    backgroundColor: '#F9FAFB',
    height: 44,
    flex: 1,
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 44,
  },
  input: {
    flex: 1,
    fontSize: 12,
    fontFamily: Fonts.Medium,
    color: Colors.black,
  },
  iconButton: {
    paddingHorizontal: 6,
  },
});
