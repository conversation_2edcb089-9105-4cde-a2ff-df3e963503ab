import {
  Image,
  StyleSheet,
  Text,
  View,
  TextInputProps,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import {images} from '../../assets/images';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {FC} from 'react';
import {IUser} from '../../interfaces/IUser';

interface Props extends TextInputProps {
  title: string;
  onPress?: () => void;
  isCompleted?: boolean;
  image?: IUser[];
  date: string;
  incentive: number;
}

const OngoingTaskList: FC<Props> = ({
  title,
  onPress,
  isCompleted,
  image,
  date,
  incentive,
}) => {
  return (
    <TouchableOpacity style={styles.mainContainer} onPress={onPress}>
      <View style={styles.titleContainer}>
        <Text style={styles.title} numberOfLines={1}>
          {title}
        </Text>
        {incentive ? (
          <View style={styles.numContainer}>
            <Image
              style={styles.staricon}
              source={images.staricon}
              resizeMode="contain"
            />
            <Text style={styles.numText}>{incentive}</Text>
          </View>
        ) : null}
      </View>
      <Text style={styles.assigntext}> Assigned to</Text>
      <View style={styles.bottomContainer}>
        <View style={styles.subbottomContainer}>
          <View style={{marginTop: 7}}>
            <ScrollView horizontal>
              {image?.map((img, index) => (
                <Image
                  key={index}
                  source={
                    img.profileImage?.url
                      ? {uri: img.profileImage.url}
                      : images.avatarPlaceholder
                  }
                  style={[styles.assignicon, {marginLeft: index == 0 ? 0 : -5}]}
                />
              ))}
            </ScrollView>
          </View>
          <Text style={styles.dateStyle}>Due on : {date}</Text>
        </View>

        {isCompleted ? (
          <View style={styles.completeContainer}>
            <Text style={styles.CompText}>Completed</Text>
            <Image style={styles.CompImg} source={images.completeicon} />
          </View>
        ) : (
          <View style={styles.completeContainer}>
            <Text style={styles.CompText}>Pending</Text>
            <Image
              style={[styles.CompImg, {tintColor: Colors.buttonbgcolor}]}
              source={images.hourglass}
            />
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    backgroundColor: Colors.white,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginTop: 12,
    marginHorizontal: 22,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 14,
    fontFamily: Fonts.Semibold,
    color: Colors.primary,
    width: '80%',
  },
  numContainer: {
    flexDirection: 'row',
  },
  staricon: {
    width: 12,
    height: 12,
    marginTop: 3,
    marginRight: 2,
  },
  numText: {
    fontSize: 14,
    fontFamily: Fonts.Regular,
    color: Colors.primary,
  },
  assigntext: {
    fontSize: 10,
    fontFamily: Fonts.Regular,
    color: Colors.primary,
    marginTop: 4,
  },
  subbottomContainer: {
    flexDirection: 'column',
  },
  assignicon: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: Colors.buttonbgcolor,
    borderRadius: 40,
  },
  dateStyle: {
    fontSize: 8,
    fontFamily: Fonts.Regular,
    color: Colors.primary,
    marginTop: 6,
  },
  bottomContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  circularProgressContainer: {
    alignSelf: 'center',
  },
  completeContainer: {
    flexDirection: 'row',
    marginTop: 20,
  },
  textPersent: {
    fontSize: 10,
    fontFamily: Fonts.Semibold,
    color: Colors.primary,
  },
  CompText: {
    fontSize: 9,
    fontFamily: Fonts.Medium,
    color: Colors.primary,
  },
  CompImg: {
    width: 16,
    height: 13,
    marginLeft: 5,
  },
});

export default OngoingTaskList;
