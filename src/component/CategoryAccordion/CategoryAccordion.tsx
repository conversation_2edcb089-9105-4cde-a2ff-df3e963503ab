import React, {useEffect} from 'react';
import {Text, TouchableOpacity, StyleSheet, Image} from 'react-native';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {ChevronRight2} from '../../assets/svgIcons';
import {ICategory} from '../../interfaces/IIssue';
import Animated, {
  FadeInDown,
  FadeOut,
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
  interpolateColor,
  FadeIn,
} from 'react-native-reanimated';
import {subCatProps} from '../../screens/appflow/CreateNewTask/SelectCategory';

interface Props {
  category: ICategory;
  isOpen: boolean;
  onPressAccordion: () => void;
  onSubcategorySelect: (cat: subCatProps) => void;
  selectedSubcategories: subCatProps[];
}

export const CategoryAccordion: React.FC<Props> = ({
  category,
  isOpen,
  onPressAccordion,
  onSubcategorySelect,
  selectedSubcategories,
}) => {
  const _damping = 14;

  const Touchable = Animated.createAnimatedComponent(TouchableOpacity);
  const _entering = FadeInDown.springify().damping(_damping);
  const _existing = FadeOut.springify().damping(_damping);
  const _layout = LinearTransition.springify().damping(_damping);

  const rotation = useSharedValue(isOpen ? 90 : 0);
  const scale = useSharedValue(isOpen ? 1.6 : 1);

  useEffect(() => {
    rotation.value = withTiming(isOpen ? 90 : 0, {
      duration: 250,
      easing: Easing.out(Easing.ease),
    });
  }, [isOpen]);

  const rotateStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          rotate: `${rotation.value}deg`,
        },
      ],
    };
  });

  useEffect(() => {
    scale.value = withTiming(isOpen ? 1.8 : 1, {
      duration: 500,
      easing: Easing.out(Easing.ease),
    });
  }, [isOpen]);

  const iconAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{scale: scale.value}],
    };
  });

  const progress = useSharedValue(isOpen ? 1 : 0);

  useEffect(() => {
    progress.value = withTiming(isOpen ? 1 : 0, {duration: 300});
  }, [isOpen]);

  const animatedWrapperStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      progress.value,
      [0, 1],
      [Colors.black, '#FAFAFA'],
    );

    return {
      backgroundColor,
    };
  });

  return (
    <Animated.View
      entering={_entering}
      exiting={_existing}
      style={styles.container}>
      <Touchable
        activeOpacity={0.8}
        onPress={onPressAccordion}
        style={[styles.categoryRow, isOpen && styles.categoryRowOpen]}>
        <Animated.View style={[styles.iconWrapper, animatedWrapperStyle]}>
          <Animated.Image
            resizeMode="contain"
            tintColor={isOpen ? Colors.buttonbgcolor : Colors.white}
            source={{
              uri: category.icon.url,
            }}
            style={[styles.icon, iconAnimatedStyle]}
          />
        </Animated.View>
        <Text style={styles.title}>{category.title}</Text>
        <Animated.View style={rotateStyle}>
          <ChevronRight2
            width={8}
            height={12}
            stroke={isOpen ? Colors.buttonbgcolor : '#555E67'}
          />
        </Animated.View>
      </Touchable>

      {isOpen && (
        <Animated.View
          entering={_entering}
          exiting={_existing}
          layout={_layout}
          style={styles.subCategoryWrapper}>
          {category.subcategories.map(item => {
            const isSelected = selectedSubcategories?.some(
              subCat => subCat.id === item.id,
            );
            return (
              <TouchableOpacity
                key={item.id}
                onPress={() =>
                  onSubcategorySelect({
                    id: item.id,
                    subCategoryName: item.title,
                  })
                }
                style={[
                  styles.subCategory,
                  isSelected && styles.subCategorySelected,
                ]}>
                <Text
                  style={[
                    styles.subCategoryText,
                    isSelected && styles.subCategoryTextSelected,
                  ]}>
                  {item.title}
                </Text>
              </TouchableOpacity>
            );
          })}
        </Animated.View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
  },
  categoryRow: {
    backgroundColor: '#FAFAFA',
    borderRadius: 12,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ECEDF0',
    height: 48,
  },
  categoryRowOpen: {
    borderColor: Colors.buttonbgcolor,
  },
  iconWrapper: {
    width: 32,
    height: 32,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  icon: {
    height: 16,
    width: 16,
  },
  iconActive: {
    width: 26,
    height: 26,
  },
  title: {
    flex: 1,
    fontSize: 12,
    fontFamily: Fonts.Medium,
    color: Colors.black,
  },
  chevronWrapper: {
    width: 36,
    height: 36,
    borderRadius: 7,
    backgroundColor: '#EFEFEF',
    alignItems: 'center',
    justifyContent: 'center',
  },

  subCategoryWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 12,
  },
  subCategory: {
    borderWidth: 1,
    borderColor: '#9393934D',
    borderRadius: 8,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
  },
  subCategorySelected: {
    borderColor: Colors.buttonbgcolor,
  },
  subCategoryText: {
    color: '#000',
    fontSize: 12,
  },
  subCategoryTextSelected: {
    color: Colors.buttonbgcolor,
  },
});
