import React, {
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
} from 'react';
import {StyleSheet, Text, Button, View, Image, Platform} from 'react-native';
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetScrollView,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import {IIssue} from '../../interfaces/IIssue';
import {appStyles, Colors, Fonts, sizes} from '../../utilities/theme/theme';
import moment from 'moment';
import AppSlider from '../common/AppSlider/AppSlider';
import {getColorByStatus} from '../../helpers/getColorByStatus';

type Props = {
  onClose?: () => void;
  issue: IIssue;
};

export type IssueDetailsSheetRef = {
  open: () => void;
  close: () => void;
};

const IssueDetailsSheet = forwardRef<IssueDetailsSheetRef, Props>(
  ({onClose, issue}, ref) => {
    const bottomSheetRef = useRef<BottomSheet>(null);
    const snapPoints = useMemo(() => ['90%'], []); // Snap to 90% height

    useImperativeHandle(ref, () => ({
      open: () => bottomSheetRef.current?.snapToIndex(0),
      close: () => bottomSheetRef.current?.close(),
    }));

    const renderBackdrop = useCallback(
      (props: BottomSheetBackdropProps) => (
        <BottomSheetBackdrop
          {...props}
          style={[props.style]}
          disappearsOnIndex={-1}
          appearsOnIndex={0}
          opacity={0.2}
          pressBehavior="close"
        />
      ),
      [],
    );
    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={-1} // Do not open the sheet by default
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        // enableContentPanningGesture={false} // prevent resizing with content
        // enableHandlePanningGesture={false} // prevent resizing with handle
        activeOffsetY={[-10, 10]} // Allows vertical movement before activating gesture
        failOffsetX={[-20, 20]} // Allows horizontal movement without closing
        enablePanDownToClose={true} // Disable swipe-to-close (use buttons instead)
        onChange={index => {
          if (index === -1 && onClose) onClose();
        }}>
        <BottomSheetScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{
            paddingBottom: Platform.OS === 'android' ? 50 : 0,
          }}>
          <BottomSheetView style={styles.contentContainer}>
            {/* Status tag */}
            <View
              style={[
                styles.statusContainer,
                {backgroundColor: getColorByStatus(issue.status)},
              ]}>
              <Text style={styles.statusText}>{issue.status}</Text>
            </View>

            {/* Main heading */}
            <Text style={[appStyles.h4, {marginTop: 16}]}>
              {issue.categoryStructure?.parentCategory.title}
            </Text>

            {/* Date when the issue was created */}
            <Text style={styles.paragraph}>
              {moment(issue.createdAt).format('ddd DD MMM YYYY')}
            </Text>

            {/* Image slider for attached issue images */}
            <AppSlider
              images={
                issue.attachment.map(item => item.url).filter(url => url) || []
              }
            />

            {/* Description section */}
            <View style={[styles.discriptionContainer, {marginTop: 24}]}>
              <Text style={styles.title}>Description</Text>
              <Text style={styles.description}>{issue.note}</Text>
            </View>

            {/* Subcategory title with icon */}
            <View style={styles.categoryContainer}>
              <Image
                source={{uri: issue.categoryStructure?.parentCategory.url}}
                style={{width: 20, height: 20}}
              />
              <Text style={[appStyles.h4, {color: '#080C10'}]}>
                Sub Category
              </Text>
            </View>

            {/* List of subcategories */}
            <View
              style={[
                styles.discriptionContainer,
                {marginBottom: 40, marginTop: 12},
              ]}>
              {issue.categoryStructure?.subCategories.map((cat, index) => (
                <Text
                  key={index} // Always add a key when rendering lists
                  style={[
                    styles.title,
                    {
                      marginBottom:
                        index ===
                        (issue.categoryStructure?.subCategories?.length ?? 0) -
                          1
                          ? 0
                          : 6, // Adds spacing between items except the last one
                      paddingTop: 0,
                    },
                  ]}>
                  {`${index + 1}.  ${cat}`}
                </Text>
              ))}
            </View>
          </BottomSheetView>
        </BottomSheetScrollView>
      </BottomSheet>
    );
  },
);

export default IssueDetailsSheet;

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 12,
    backgroundColor: Colors.white,
  },
  statusContainer: {
    backgroundColor: '#F5ED57',
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 3,
    borderRadius: 4,
  },
  statusText: {
    color: Colors.white,
    fontFamily: Fonts.Medium,
    fontSize: 10,
    textTransform: 'capitalize',
  },
  paragraph: {
    fontSize: 14,
    color: '#475467',
    paddingTop: 2,
    fontFamily: Fonts.Medium,
  },
  discriptionContainer: {
    borderWidth: 1,
    borderColor: '#EAECF0',
    padding: 12,
    borderRadius: 12,
  },
  title: {
    fontSize: 12,
    color: Colors.primary_text,
    paddingTop: 2,
    fontFamily: Fonts.Semibold,
  },
  description: {
    fontSize: 10,
    color: '#475467',
    paddingTop: 4,
    fontFamily: Fonts.Medium,
  },
  categoryContainer: {
    marginTop: 24,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
});
