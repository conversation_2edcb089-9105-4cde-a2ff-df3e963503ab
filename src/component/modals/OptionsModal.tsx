import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ViewStyle,
  TextStyle,
  Modal,
  Pressable,
  ActivityIndicator,
} from 'react-native';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {Button} from '../Button/Button';
import {CircleFill, CircleStroke} from '../../assets/svgIcons';

interface Option {
  value: string;
  id: string;
}
interface OptionsModalProps {
  visible: boolean;
  title: string;
  description: string;
  options: Option[];
  selectedValue: string;
  onSelect: (value: Option) => void;
  onCancel: () => void;
  onRequest: () => void;
  isRequestDisabled?: boolean;
  containerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  descriptionStyle?: TextStyle;
  loading?: boolean;
}

const OptionsModal: React.FC<OptionsModalProps> = ({
  visible,
  title,
  description,
  options,
  selectedValue,
  onSelect,
  onCancel,
  onRequest,
  isRequestDisabled = false,
  containerStyle,
  titleStyle,
  descriptionStyle,
  loading = false,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent
      onRequestClose={onCancel}>
      <Pressable style={styles.overlay} onPress={onCancel}>
        <Pressable
          style={[styles.container, containerStyle]}
          onPress={() => {}}>
          {loading ? (
            <ActivityIndicator
              color={Colors.buttonbgcolor}
              style={{height: 250, paddingBottom: 100}}
            />
          ) : (
            <>
              {/* Header */}
              <Text style={[styles.title, titleStyle]}>{title}</Text>
              <Text style={[styles.description, descriptionStyle]}>
                {description}
              </Text>

              {/* Options */}
              <FlatList
                data={options}
                keyExtractor={item => item.id}
                contentContainerStyle={styles.flatListContent}
                renderItem={({item}) => (
                  <TouchableOpacity
                    style={[
                      styles.optionContainer,
                      selectedValue === item.value && styles.optionSelected,
                    ]}
                    onPress={() => onSelect(item)}>
                    <Text style={styles.optionLabel}>{item.value}</Text>
                    {selectedValue === item.value ? (
                      <CircleFill />
                    ) : (
                      <CircleStroke />
                    )}
                  </TouchableOpacity>
                )}
              />

              {/* Actions */}
              <View style={styles.buttonRow}>
                <Button
                  title="Cancel"
                  containerStyle={styles.cancelBtn}
                  textContainer={styles.cancelBtnText}
                  onPress={onCancel}
                />
                <Button
                  title="Select"
                  disabled={!isRequestDisabled}
                  containerStyle={styles.confirmBtn}
                  onPress={onRequest}
                />
              </View>
            </>
          )}
        </Pressable>
      </Pressable>
    </Modal>
  );
};

export default OptionsModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: Colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingTop: 20,
  },
  title: {
    fontSize: 20,
    fontFamily: Fonts.Semibold,
    color: Colors.primary_text,
    paddingHorizontal: 24,
  },
  description: {
    fontSize: 14,
    color: '#667085',
    marginTop: 2,
    paddingHorizontal: 24,
  },
  flatListContent: {
    gap: 8,
    marginTop: 24,
    paddingHorizontal: 24,
  },
  optionContainer: {
    height: 56,
    borderWidth: 1,
    borderColor: '#98A2B3',
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  optionSelected: {
    borderColor: '#BE935E',
    backgroundColor: '#BE935E12',
  },
  optionLabel: {
    fontSize: 14,
    color: Colors.primary_text,
    fontFamily: Fonts.Regular,
    textTransform: 'capitalize',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 32,
    gap: 12,
    paddingBottom: 26,
    paddingTop: 14,
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    backgroundColor: Colors.white,
  },
  cancelBtn: {
    flex: 0.5,
    height: 48,
    backgroundColor: Colors.white,
  },
  cancelBtnText: {
    color: Colors.buttonbgcolor,
  },
  confirmBtn: {
    flex: 0.5,
    height: 48,
  },
});
