import React, {useContext, useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  ViewStyle,
  Modal,
  Pressable,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import {Colors, Fonts} from '../../utilities/theme/theme';
import {Button} from '../Button/Button';
import {CircleFill, CircleStroke} from '../../assets/svgIcons';
import {IIssue} from '../../interfaces/IIssue';
import {getIssuesByManagerId} from '../../backend/Issues';
import {AuthContext} from '../../../App';

const {height: screenHeight} = Dimensions.get('window');

interface Option {
  value: string;
  id: string;
}

interface ChooseIssueModalProps {
  visible: boolean;
  selectedValue: string;
  onSelect: (id: string, parentCatName: string) => void;
  onCancel: () => void;
  onRequest: () => void;
  isRequestDisabled?: boolean;
}

const ChooseIssueModal: React.FC<ChooseIssueModalProps> = ({
  visible,
  selectedValue,
  onSelect,
  onCancel,
  onRequest,
  isRequestDisabled = false,
}) => {
  const {userId} = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const [managerIssues, setManagerIssues] = useState<IIssue[]>([]);
  const [searchText, setSearchText] = useState('');

  useEffect(() => {
    if (!userId) return;

    const fetch = async () => {
      setLoading(true);
      const issues = await getIssuesByManagerId(userId); // Pass `limit` if needed
      setManagerIssues(issues);
      setLoading(false);
    };

    fetch();
  }, [userId]);

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent
      onRequestClose={onCancel}>
      <Pressable style={styles.overlay} onPress={onCancel}>
        <Pressable style={[styles.container]} onPress={() => {}}>
          {loading ? (
            <ActivityIndicator
              color={Colors.buttonbgcolor}
              style={{height: 250, paddingBottom: 100}}
            />
          ) : (
            <>
              <Text style={[styles.title]}>Choose an Issue</Text>
              <Text style={[styles.description]}>
                Select one of the issues from the list below.{' '}
              </Text>

              <FlatList
                data={managerIssues}
                keyExtractor={item => item.id}
                contentContainerStyle={styles.flatListContent}
                style={{maxHeight: screenHeight * 0.5}} // Dynamic height up to 50% screen
                showsVerticalScrollIndicator={false}
                renderItem={({item}) => {
                  const isSelected = item.id === selectedValue;
                  return (
                    <TouchableOpacity
                      onPress={() =>
                        onSelect(
                          item.id,
                          item.categoryStructure?.parentCategory.title || '',
                        )
                      }
                      style={[
                        styles.optionContainer,
                        isSelected && styles.optionSelected,
                      ]}>
                      <View style={{flex: 0.8}}>
                        <Text numberOfLines={1} style={styles.titleSyles}>
                          {item.categoryStructure?.parentCategory.title || ''}
                        </Text>
                        <Text numberOfLines={2} style={styles.optionLabel}>
                          {item.note}
                        </Text>
                      </View>

                      {isSelected ? <CircleFill /> : <CircleStroke />}
                    </TouchableOpacity>
                  );
                }}
              />

              <View style={styles.buttonRow}>
                <Button
                  title="Cancel"
                  containerStyle={styles.cancelBtn as ViewStyle}
                  textContainer={styles.cancelBtnText}
                  onPress={onCancel}
                />
                <Button
                  title="Request"
                  containerStyle={styles.confirmBtn}
                  onPress={onRequest}
                  disabled={isRequestDisabled}
                />
              </View>
            </>
          )}
        </Pressable>
      </Pressable>
    </Modal>
  );
};

export default ChooseIssueModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'flex-end',
  },
  container: {
    backgroundColor: Colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingTop: 20,
  },
  title: {
    fontSize: 20,
    fontFamily: Fonts.Semibold,
    color: Colors.primary_text,
    paddingHorizontal: 24,
  },
  description: {
    fontSize: 14,
    color: '#667085',
    marginTop: 2,
    paddingHorizontal: 24,
  },
  flatListContent: {
    gap: 8,
    marginTop: 24,
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  optionContainer: {
    borderWidth: 1,
    borderColor: '#98A2B3',
    borderRadius: 8,
    paddingHorizontal: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
    paddingVertical: 12,
  },
  optionSelected: {
    borderColor: '#BE935E',
    backgroundColor: '#BE935E12',
  },
  titleSyles: {
    fontSize: 16,
    color: Colors.primary_text,
    fontFamily: Fonts.Medium,
  },
  optionLabel: {
    fontSize: 14,
    color: Colors.primary_text,
    fontFamily: Fonts.Regular,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 32,
    gap: 12,
    paddingBottom: 26,
    paddingTop: 14,
    paddingHorizontal: 24,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    backgroundColor: Colors.white,
  },
  cancelBtn: {
    flex: 0.5,
    height: 48,
    backgroundColor: Colors.white,
  },
  cancelBtnText: {
    color: Colors.buttonbgcolor,
  },
  confirmBtn: {
    flex: 0.5,
    height: 48,
  },
});
