// CreateIssueModal.tsx
import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import Modal from 'react-native-modal';
import {Calendar} from '../../assets/svgIcons';
import {Button} from '../Button/Button';
import {Colors, Fonts} from '../../utilities/theme/theme';

interface CreateIssueModalProps {
  isVisible: boolean;
  onClose: () => void;
  onProceed: () => void;
  title: string;
  subtitle: string;
  proceedButtonTitle: string;
  cancelButtonTitle?: string;
  showCancelButton?: boolean;
}

const CreateIssueModal: React.FC<CreateIssueModalProps> = ({
  isVisible,
  onClose,
  onProceed,
  title,
  subtitle,
  proceedButtonTitle,
  cancelButtonTitle = 'Cancel',
  showCancelButton = false,
}) => {
  return (
    <Modal
      style={{margin: 0, justifyContent: 'flex-end'}}
      isVisible={isVisible}
      onBackdropPress={onClose}
      backdropTransitionOutTiming={0}
      useNativeDriverForBackdrop={true}>
      <View style={styles.modalContainer}>
        <View style={styles.iconBox}>
          <Calendar />
        </View>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.subtitle}>{subtitle}</Text>

        <Button title={proceedButtonTitle} onPress={onProceed} />

        {showCancelButton && (
          <Button
            onPress={onClose}
            title={cancelButtonTitle}
            containerStyle={styles.cancelButtonContainer}
            textContainer={styles.cancelText}
          />
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 28,
    paddingTop: 85,
    paddingBottom: 42,
  },
  iconBox: {
    backgroundColor: '#b68e5c',
    borderRadius: 24,
    position: 'absolute',
    alignSelf: 'center',
    top: -50,
    width: 100,
    height: 100,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 20,
    fontFamily: Fonts.Semibold,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    color: '#393B41',
    marginTop: 16,
    marginBottom: 24,
  },
  cancelButtonContainer: {
    marginTop: 16,
    backgroundColor: Colors.white,
    borderWidth: 1,
    borderColor: Colors.buttonbgcolor,
  },
  cancelText: {
    color: Colors.buttonbgcolor,
  },
});

export default CreateIssueModal;
