import {
  Image,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import React, {useContext} from 'react';
import {AuthContext} from '../../../App';
import {Fonts, sizes} from '../../utilities/theme/theme';
import {Colors} from 'react-native/Libraries/NewAppScreen';
import {images} from '../../assets/images';
import {UserAvatar} from '../../assets/svgIcons';

interface Props {
  containerStyle?: ViewStyle;
  paddingHorizontal?: number;
  onPressNotification: () => void;
  onPressProfile: () => void;
}

const CustomHeader: React.FC<Props> = ({
  containerStyle,
  paddingHorizontal = sizes.paddingHorizontal,
  onPressNotification,
  onPressProfile,
}) => {
  const {userData} = useContext(AuthContext);
  return (
    <View
      style={[
        styles.headerContainer,
        containerStyle,
        {
          paddingHorizontal,
        },
      ]}>
      <View style={styles.textContainer}>
        <Text style={styles.titleStyle}>Welcome Back!</Text>
        <Text style={styles.subTitle}>{userData.name}</Text>
      </View>
      <View style={{flexDirection: 'row'}}>
        <TouchableOpacity
          style={{alignSelf: 'center'}}
          onPress={onPressNotification}>
          <Image
            style={styles.notificationicon}
            source={images.notificationicon}
            resizeMode="contain"
          />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={onPressProfile}
          style={styles.imageContainer}>
          {userData?.profileImage?.url ? (
            <Image
              style={styles.headlogo}
              source={{uri: userData?.profileImage?.url}}
            />
          ) : (
            <UserAvatar width={19} height={19} />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default CustomHeader;

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  textContainer: {
    flexDirection: 'column',
  },
  titleStyle: {
    fontSize: 14,
    fontFamily: Fonts.Semibold,
    color: Colors.lightcolor,
  },
  subTitle: {
    color: 'black',
    fontSize: 18,
    fontFamily: Fonts.Bold,
  },
  headlogo: {
    width: 38,
    height: 38,
    borderRadius: 100,
  },
  notificationicon: {
    width: 24,
    height: 24,
    marginRight: 8,
    alignSelf: 'center',
  },
  searchContainer: {
    marginTop: 24,
    borderRadius: sizes.borderRadius,
  },
  imageContainer: {
    width: 38,
    height: 38,
    borderRadius: 38 / 2,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1.3,
    borderColor: '#7B7B7B',
  },
});
