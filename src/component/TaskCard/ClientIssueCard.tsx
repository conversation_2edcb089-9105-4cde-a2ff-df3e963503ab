import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  ImageSourcePropType,
} from 'react-native';
import {appStyles, Colors, Fonts} from '../../utilities/theme/theme';
import {
  AttachmentStroke,
  Clock,
  Communication,
  SavedStroke,
  VerticalDot,
} from '../../assets/svgIcons';
import ProgressBar from '../common/ProgressBar/ProgressBar';

type Avatar = {
  uri: string;
};

type TaskCardProps = {
  timeLeft: string;
  priority: string;
  status: string;
  title: string;
  commentsCount: number;
  attachmentsCount: number;
  time: string;
  avatars: Avatar[];
};

const ClientIssueCard: React.FC<TaskCardProps> = ({
  timeLeft,
  priority,
  status,
  title,
  commentsCount,
  attachmentsCount,
  time,
  avatars,
}) => {
  return (
    <View style={styles.card}>
      {/* Top section with labels and icons */}
      <View style={[appStyles.flexSpaceBetween, styles.topRow]}>
        <View style={[appStyles.flexRow, styles.labelGroup]}>
          <Text style={[styles.label, styles.red]}>{timeLeft}</Text>
          <Text style={[styles.label, styles.brown]}>{priority}</Text>
          <Text style={[styles.label, styles.yellow]}>{status}</Text>
        </View>
        <View style={[appStyles.flexRow, styles.iconGroup]}>
          <TouchableOpacity hitSlop={8} style={styles.menuIcon}>
            <VerticalDot />
          </TouchableOpacity>
          <TouchableOpacity hitSlop={8} style={styles.menuIcon}>
            <SavedStroke />
          </TouchableOpacity>
        </View>
      </View>

      {/* Title */}
      <Text numberOfLines={2} style={styles.title}>
        {title}
      </Text>

      {/* Progress bar */}
      <View style={[appStyles.flexSpaceBetween, {marginTop: 12}]}>
        <Text style={styles.progressText}>Progress</Text>
        <Text style={styles.text}>80%</Text>
      </View>

      <ProgressBar
        max={100}
        value={80}
        containerStyle={{marginTop: 4}}
        filledStyle={styles.filledStyle}
        unfilledStyle={styles.unfilledStyle}
        showBottomText={false}
      />

      {/* Bottom row with avatars and meta info */}
      <View style={styles.footerRow}>
        <View style={styles.avatarGroup}>
          {avatars.map((avatar, index) => (
            <Image
              key={index}
              source={{uri: avatar.uri}}
              style={styles.avatar}
            />
          ))}
        </View>
        <View style={styles.metaRow}>
          <TouchableOpacity style={styles.metaItem}>
            <Communication />
            <Text style={styles.metaText}>{commentsCount}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.metaItem}>
            <AttachmentStroke />
            <Text style={styles.metaText}>{attachmentsCount}</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.metaItem}>
            <Clock />
            <Text style={styles.metaText}>{time}</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

export default ClientIssueCard;

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.buttonbgcolor,
    borderRadius: 16,
    padding: 12,
    width: Dimensions.get('screen').width - 70,
  },
  topRow: {
    gap: 26,
  },
  labelGroup: {
    gap: 6,
  },
  iconGroup: {
    gap: 16,
  },
  label: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 5,
    color: Colors.white,
    fontSize: 7,
    fontFamily: Fonts.Medium,
    paddingTop: 4,
  },
  red: {
    backgroundColor: '#B62023',
  },
  brown: {
    backgroundColor: '#6E5043',
  },
  yellow: {
    backgroundColor: '#F5ED57',
    color: '#080C10',
  },
  menuIcon: {},
  title: {
    color: Colors.white,
    fontSize: 16,
    fontFamily: Fonts.Bold,
    paddingTop: 12,
    marginRight: 80,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.white,
    opacity: 0.3,
    marginTop: 16,
    marginBottom: 12,
  },
  footerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
  },
  avatarGroup: {
    flexDirection: 'row',
  },
  avatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 1.5,
    borderColor: Colors.white,
    marginRight: -7,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  metaText: {
    color: '#4B5563',
    fontSize: 10,
    fontFamily: Fonts.Medium,
    paddingTop: 2,
  },
  unfilledStyle: {
    backgroundColor: Colors.white,
    height: 6,
    borderRadius: 0,
  },
  text: {
    fontSize: 10,
    color: Colors.white,
    fontFamily: Fonts.Semibold,
  },
  progressText: {
    fontSize: 8,
    color: Colors.white,
    fontFamily: Fonts.Medium,
  },
  filledStyle: {
    backgroundColor: '#48D25E',
    height: 6,
    borderRadius: 0,
  },
});
