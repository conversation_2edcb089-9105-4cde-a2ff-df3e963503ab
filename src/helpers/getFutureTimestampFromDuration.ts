import firestore from '@react-native-firebase/firestore';

/**
 * Converts a duration string like "48 hours" to a Firestore Timestamp
 * @param durationStr - Duration string (e.g. "48 hours", "72 hours", "96 hours")
 * @returns Firestore.Timestamp representing the future date/time
 */
export const getFutureTimestampFromDuration = (durationStr: string) => {
  // Extract number of hours from string
  const hours = parseInt(durationStr.trim());

  if (isNaN(hours)) {
    throw new Error(`Invalid duration string: "${durationStr}"`);
  }

  // Current time
  const now = new Date();

  // Future time by adding hours
  const futureDate = new Date(now.getTime() + hours * 60 * 60 * 1000);

  // Convert to Firestore Timestamp
  return firestore.Timestamp.fromDate(futureDate);
};
