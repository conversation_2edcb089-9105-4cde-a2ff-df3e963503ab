import {FirebaseFirestoreTypes} from '@react-native-firebase/firestore';
import moment from 'moment'; // or use Date if you prefer

export const getHoursLeft = (
  durationTimeStamp: FirebaseFirestoreTypes.Timestamp,
): number => {
  const endDate = durationTimeStamp.toDate(); // Convert Firestore Timestamp to JS Date
  const now = new Date();

  const diffInMs = endDate.getTime() - now.getTime(); // Milliseconds difference
  const diffInHours = diffInMs / (1000 * 60 * 60); // Convert to hours

  return Math.max(0, Math.floor(diffInHours)); // Clamp to 0 minimum, round down
};
