import {firebase} from '@react-native-firebase/firestore';

export const uploadMultipleAttachment = async (
  attachments: {uri: string; type: string; name: string}[],
  userId: string,
) => {
  const uploadedAttachments = await Promise.all(
    attachments.map(async attachment => {
      const randomId = Math.floor(Math.random() * 1000000000);
      const filePath = `users/${userId}/attachments/${userId}_${randomId}_img`;
      const reference = firebase.storage().ref(filePath);

      await reference.putFile(attachment.uri);
      const url = await reference.getDownloadURL();

      return {
        url: url,
        path: filePath,
      };
    }),
  );

  return uploadedAttachments;
};
