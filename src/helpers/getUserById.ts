import firestore from '@react-native-firebase/firestore';
import {IUser} from '../interfaces/IUser';
export const getUserById = async (userId: string): Promise<IUser | null> => {
  try {
    const userDoc = await firestore().collection('Users').doc(userId).get();

    if (userDoc.exists) {
      return {id: userDoc.id, ...userDoc.data()} as IUser;
    } else {
      console.warn('User not found');
      return null;
    }
  } catch (error) {
    console.error('Error fetching user:', error);
    return null;
  }
};
