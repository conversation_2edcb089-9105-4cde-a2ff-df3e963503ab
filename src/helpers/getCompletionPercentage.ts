import {ITask} from '../interfaces/ITask';

export function getCompletionPercentage(
  userId: string,
  tasks: ITask[],
): string {
  // Filter tasks assigned to the given user
  const userTasks = tasks.filter(t => t.staffId === userId);

  // If no tasks found for the user, return "0.00" as a string
  if (userTasks.length === 0) return '0';

  // Filter only the completed tasks from the user's task list
  const completedTasks = userTasks.filter(t => t.status === 'completed');

  // Calculate percentage of completed tasks
  const percentage = (completedTasks.length / userTasks.length) * 100;

  // Round the result to the nearest integer and convert it to string
  return Math.round(percentage).toString();
}
