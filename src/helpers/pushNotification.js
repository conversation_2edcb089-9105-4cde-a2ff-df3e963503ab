import messaging from '@react-native-firebase/messaging';
import {useEffect} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {PermissionsAndroid} from 'react-native';

export const NotificationHandlerHOC = RootNavigator => {
  const MessageHandlerComponent = () => {
    const getFCMToken = async () => {
      try {
        const fcmToken = await messaging().getToken();

        if (fcmToken) {
          try {
            await AsyncStorage.setItem('fcmToken', fcmToken);
          } catch (error) {
            console.log('Error saving FCM token to Firestore:', error);
            throw error;
          }
        }
      } catch (error) {
        console.log('Error in fetching fcmtoken', error);
      }
    };
    const notificationListener = async () => {
      messaging().onNotificationOpenedApp(remoteMessage => {
        console.log(
          'Notification caused app to open from background state:',
          remoteMessage.data,
        );
      });
      messaging()
        .getInitialNotification()
        .then(remoteMessage => {
          if (remoteMessage) {
            console.log(
              'Notification caused app to open from quit state:',
              remoteMessage.notification,
            );
          }
        });
      messaging().onMessage(async remoteMessage => {
        console.log('notification on foreground state....', remoteMessage);
      });
    };
    const requestUserPermission = async () => {
      if (Platform.OS === 'ios') {
        const authStatus = await messaging().requestPermission();
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (enabled) {
          getFCMToken();
        }
      } else if (Platform.OS === 'android') {
        const hasPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        );

        if (!hasPermission) {
          const status = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
          );

          if (status === PermissionsAndroid.RESULTS.GRANTED) {
            getFCMToken();
          }
        } else {
          getFCMToken();
        }
      } else {
        getFCMToken();
      }
    };
    messaging().setBackgroundMessageHandler(async remoteMessage => {
      console.log('Message handled in the background!', remoteMessage);
    });

    useEffect(() => {
      requestUserPermission();
      notificationListener();
    }, []);
    return <RootNavigator />;
  };
  return MessageHandlerComponent;
};
