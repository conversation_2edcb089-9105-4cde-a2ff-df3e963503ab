import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {appStyles, Colors, Fonts, sizes} from '../../../utilities/theme/theme';
import ProgressBar from '../../../component/common/ProgressBar/ProgressBar';
import MultiLineNoteInput from '../../../component/common/MultiLineNoteInput';
import ImagePicker from 'react-native-image-crop-picker';
import {Button} from '../../../component/Button/Button';
import {Attachment, CrossRed} from '../../../assets/svgIcons';
import firestore from '@react-native-firebase/firestore';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {uploadMultipleAttachment} from '../../../helpers/uploadMultipleAttachment';
import {AuthContext} from '../../../../App';
import CreateIssueModal from '../../../component/modals/CreateIssueModal';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import AttachmentComp from '../../../component/common/AttachmentComp/AttachmentComp';
import {IUser} from '../../../interfaces/IUser';
type Props = NativeStackScreenProps<HomeStackParamsList, 'SelectAttachment'>;

const MAX_ATTACHMENTS = 3;

type FormValues = {
  notes: string;
  attachments: {uri: string; type: string; name: string}[];
};

const SelectAttachment: React.FC<Props> = ({navigation, route}) => {
  const {category, selectedCatIds} = route.params;
  const {userId} = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setModalVisible] = useState(false);
  const [isDoneModalVisible, setDoneModalVisible] = useState(false);
  const [managers, setManagers] = useState<IUser[]>([]);

  const handleSubmit = async (values: FormValues) => {
    setLoading(true);

    try {
      if (!userId) throw new Error('User not logged in');

      // 🔍 Step 1: Find the best matching manager based on selected categories
      let bestManager: IUser = {} as IUser;
      let maxMatches = 0;

      managers.forEach(manager => {
        const assignedCategories = manager.categories || [];
        const matchCount = assignedCategories.filter((catId: string) =>
          selectedCatIds.includes(catId),
        ).length;

        if (matchCount > maxMatches) {
          maxMatches = matchCount;
          bestManager = manager;
        }
      });

      // if (!bestManager || !bestManager.id) {
      //   throw new Error('No manager found matching the selected categories');
      // }

      // 📤 Step 2: Upload attachments to Firebase Storage
      const uploadedAttachments = await uploadMultipleAttachment(
        values.attachments,
        userId,
      );
      const payload = {
        attachment: uploadedAttachments,
        note: values.notes,
        createdAt: firestore.FieldValue.serverTimestamp(),
        updatedAt: firestore.FieldValue.serverTimestamp(),
        status: 'todo',
        categories: selectedCatIds,
        userId: userId,
        managerId: bestManager?.id || null,
        categoryStructure: category,
      };

      // 📝 Step 3: Create the issue in Firestore
      await firestore()
        .collection('Issues')
        .add({...payload});

      // // ✅ Step 4: Show success modal
      setDoneModalVisible(true);
      console.log('Issue submitted!');
    } catch (error) {
      console.error('Submit error:', error);
    } finally {
      setLoading(false);
    }
  };
  const formik = useFormik({
    initialValues: {
      notes: '',
      attachments: [] as {uri: string; type: string; name: string}[],
    },
    validationSchema: Yup.object().shape({
      notes: Yup.string().required(),
      attachments: Yup.array()
        .min(1, 'At least one attachment is required')
        .max(MAX_ATTACHMENTS, 'Max 3 attachments'),
    }),
    onSubmit: handleSubmit,
  });

  const handleProceed = () => {
    setModalVisible(false);
    setTimeout(() => {
      formik.handleSubmit();
    }, 600);
  };

  const onCloseDoneModal = () => {
    setDoneModalVisible(false);
    setTimeout(() => {
      navigation.popToTop();
    }, 600);
  };

  const fetchAllManagers = async () => {
    try {
      const snapShot = await firestore()
        .collection('Users')
        .where('userType', '==', 'manager')
        .get();

      const users: IUser[] = snapShot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as Omit<IUser, 'id'>),
      }));
      setManagers(users);
    } catch (error) {
      console.log('errors while fetching mangers', error);
    }
  };

  useEffect(() => {
    fetchAllManagers();
  }, []);

  const handleAttachmentPick = async (index?: number) => {
    try {
      const image = await ImagePicker.openPicker({
        cropping: true,
        compressImageQuality: 0.8,
        mediaType: 'photo',
      });

      const newAttachment = {
        uri: image.path,
        type: image.mime,
        name: `image-${Date.now()}.jpg`,
      };

      const current = [...formik.values.attachments];

      if (typeof index === 'number') {
        current[index] = newAttachment; // Replace at index
      } else {
        if (current.length >= MAX_ATTACHMENTS) return;
        current.push(newAttachment);
      }

      formik.setFieldValue('attachments', current);
    } catch (error) {
      console.warn('Image picker error:', error);
    }
  };

  const handleRemoveAttachment = (index: number) => {
    const updated = [...formik.values.attachments];
    updated.splice(index, 1);
    formik.setFieldValue('attachments', updated);
  };

  return (
    <KeyboardAwareScrollView
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{flexGrow: 1}}>
      <View style={styles.container}>
        <View>
          <ProgressBar showBottomText={true} value={2} max={2} />
          <View style={styles.card}>
            <Text style={appStyles.h5}>Attachment</Text>
            <Text style={[appStyles.body5, styles.attachmentNote]}>
              Format should be in .jpeg .png less than 5MB
            </Text>
            <View style={styles.attachmentsContainer}>
              {[0, 1, 2].map(i => {
                const item = formik.values.attachments[i];
                return (
                  <AttachmentComp
                    key={i}
                    url={item?.uri}
                    onPress={() =>
                      handleAttachmentPick(
                        i < formik.values.attachments.length ? i : undefined,
                      )
                    }
                    onRemove={
                      item ? () => handleRemoveAttachment(i) : undefined
                    }
                  />
                );
              })}
            </View>
            {!!formik.errors.attachments &&
              typeof formik.errors.attachments === 'string' && (
                <Text style={styles.errorText}>
                  {formik.errors.attachments}
                </Text>
              )}
            <MultiLineNoteInput
              value={formik.values.notes}
              onChangeText={formik.handleChange('notes')}
              placeholder="Type here..."
              placeholderTextColor={'#98A2B3'}
              containerStyle={styles.noteInput}
              onBlur={formik.handleBlur('notes')}
            />
            {formik.touched.notes && formik.errors.notes && (
              <Text style={styles.errorText}>{formik.errors.notes}</Text>
            )}
          </View>
        </View>

        <Button
          title="Create Task"
          containerStyle={styles.submitButton}
          onPress={() => setModalVisible(true)}
          isLoading={loading}
          disabled={loading || !formik.dirty || !formik.isValid}
        />
      </View>
      <CreateIssueModal
        isVisible={isModalVisible}
        onClose={() => setModalVisible(false)}
        onProceed={handleProceed}
        title="Create New Issue"
        subtitle="Double-check your issue details to ensure everything is correct. Do you want to proceed?"
        proceedButtonTitle="Yes, Proceed Now"
        cancelButtonTitle="No, Let me check"
        showCancelButton={true}
      />
      <CreateIssueModal
        isVisible={isDoneModalVisible}
        onClose={onCloseDoneModal}
        onProceed={onCloseDoneModal}
        title="Issue Has Been Created!"
        subtitle="Congratulations! Issue has been created! And successfully assigned to the team"
        proceedButtonTitle="Done"
        showCancelButton={false}
      />
    </KeyboardAwareScrollView>
  );
};

export default SelectAttachment;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 16,
    justifyContent: 'space-between',
  },
  card: {
    marginTop: 20,
    backgroundColor: '#FEFEFE',
    paddingVertical: 24,
    paddingHorizontal: 16,
    borderRadius: 10,
  },
  attachmentNote: {
    paddingTop: 1,
  },
  attachmentsContainer: {
    flexDirection: 'row',
    marginTop: 16,
    justifyContent: 'space-between',
  },

  noteInput: {
    marginTop: 16,
  },
  submitButton: {
    marginBottom: 14,
    marginTop: 10,
  },
  errorText: {
    color: Colors.red,
    fontSize: 12,
    marginTop: 5,
    fontFamily: Fonts.Medium,
    textTransform: 'capitalize',
  },
});
