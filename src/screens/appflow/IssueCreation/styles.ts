import {StyleSheet} from 'react-native';
import {s, vs, ms} from 'react-native-size-matters';
import {Colors, Fonts} from '../../../utilities/theme/theme';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: s(24),
  },
  titleStyle: {
    fontSize: ms(16),
    color: Colors.primary,
    fontFamily: Fonts.Semibold,
    marginTop: vs(19),
  },
  icon: {
    height: ms(25),
    width: ms(25),
  },
  taskDetailInput: {
    height: ms(94),
    flex: 1,
    paddingTop: 12,
  },
  timeContainer: {
    height: ms(30),
    width: ms(103),
    backgroundColor: Colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  timeText: {
    fontFamily: Fonts.Medium,
    lineHeight: 18,
    fontSize: ms(12),
    color: Colors.primary,
  },
  rowContainer: {flexDirection: 'row', alignItems: 'center'},
  pasteContainerStyle: {
    marginTop: vs(26),
    justifyContent: 'space-between',
    paddingRight: 16,
  },
  timeDateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: s(27),
    marginTop: vs(12),
  },
  searchInput: {
    backgroundColor: Colors.white,
    paddingRight: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    marginTop: vs(12),
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  taskDetailStyle: {
    height: ms(94),
    padding: ms(12),
  },
  designation: {
    fontFamily: Fonts.Regular,
    fontSize: 10,
    color: Colors.buttonbgcolor,
  },
  nameText: {
    fontSize: 12,
    fontFamily: Fonts.Semibold,
    color: Colors.primary,
  },
  avatarImage: {
    height: 42,
    width: 42,
    borderWidth: 1,
    borderColor: Colors.buttonbgcolor,
    borderRadius: 100,
  },
  modalContainer: {
    backgroundColor: 'white',
    paddingHorizontal: 24,
    paddingVertical: 34,
    borderTopRightRadius: 18,
    borderTopLeftRadius: 18,
    height: '80%',
  },
  employeeName: {
    fontFamily: Fonts.Regular,
    fontSize: 10,
    color: Colors.primary,
    marginLeft: s(5),
    marginRight: 5,
  },
  doneBtn: {
    position: 'absolute',
    bottom: 60,
    width: '100%',
    alignSelf: 'center',
  },
  modalRow: {
    marginTop: 22,
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderColor: Colors.buttonbgcolor,
    borderRadius: 6,
  },
  successText: {
    fontFamily: Fonts.Semibold,
    fontSize: 24,
    color: '#27AE60',
    marginTop: 22,
  },
  successContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 50,
    paddingHorizontal: 89,
    backgroundColor: Colors.white,
    borderRadius: 16,
  },
  addContainer: {
    padding: 6,
    borderWidth: 1,
    borderColor: Colors.buttonbgcolor,
    height: 32,
    justifyContent: 'center',
    width: '90%',
  },
  addText: {
    fontFamily: Fonts.Medium,
    fontSize: 10,
    color: '#BFBFBF',
  },
  imageStyle: {
    width: 20,
    height: 20,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: Colors.buttonbgcolor,
  },
  ratingContainer: {
    marginTop: 15,
    marginLeft: 12,
  },
  buttonStyle: {marginTop: vs(26)},
  subTitleStyle: {
    fontSize: 8,
    color: Colors.subTextColor,
    marginLeft: 4,
    fontFamily: Fonts.Medium,
  },
  attachmentModalContainer: {
    padding: 5,
    backgroundColor: Colors.background,
    paddingBottom: 40,
    paddingTop: 20,
    borderTopRightRadius: 16,
    borderTopLeftRadius: 16,
  },
  attachmentButton: {
    alignItems: 'center',
    padding: 10,
    flexDirection: 'row',
    marginLeft: 20,
  },
  attachmentImage: {tintColor: Colors.buttonbgcolor, width: 20, height: 20},
  attachmentText: {
    fontFamily: Fonts.Medium,
    fontSize: 18,
    color: Colors.primary,
    marginTop: 4,
    marginLeft: 5,
  },
  divider: {
    height: 1,
    width: '86%',
    backgroundColor: Colors.placeholder,
    alignSelf: 'center',
  },
  attachmentModal: {justifyContent: 'flex-end', margin: 0},
  listContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
    gap: 10,
    width: '80%',
  },
  crossButton: {position: 'absolute', top: 5, zIndex: 10, left: 30},
  crossIcon: {height: 15, width: 15},
  attachmentimage: {
    height: 40,
    width: 40,
    marginTop: 10,
  },
});
export default styles;
