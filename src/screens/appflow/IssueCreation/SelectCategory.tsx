// Imports
import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useMemo, useState} from 'react';
import {appStyles, Colors, sizes} from '../../../utilities/theme/theme';
import {CategoryAccordion} from '../../../component/CategoryAccordion/CategoryAccordion';
import {Button} from '../../../component/Button/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {firebase} from '@react-native-firebase/firestore';
import {ICategory} from '../../../interfaces/IIssue';
import ProgressBar from '../../../component/common/ProgressBar/ProgressBar';
import {useFocusEffect} from '@react-navigation/native';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';

type Props = NativeStackScreenProps<HomeStackParamsList, 'SelectCategory'>;

interface parentCatProps {
  url: string;
  id: string;
  parentCategoryName: string;
}

export interface subCatProps {
  id: string;
  subCategoryName: string;
}

const SelectCategory: React.FC<Props> = ({navigation}) => {
  // States for managing UI and data
  const [selectedIds, setSelecteIds] = useState<string[]>([]);
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedParentCategory, setSelectedParentCatgory] =
    useState<parentCatProps | null>(null);
  const [selectedSubcategories, setSelectedSubcategories] = useState<
    subCatProps[]
  >([]);

  // Fetch categories from Firestore
  useEffect(() => {
    const unsubscribe = firebase
      .firestore()
      .collection('Categories')
      .onSnapshot(snapshot => {
        const allCategories: ICategory[] = snapshot.docs.map(doc => ({
          id: doc.id,
          ...(doc.data() as Omit<ICategory, 'id'>),
        }));

        // Separate parent and child categories
        const parentCategories = allCategories.filter(
          cat => cat.parentCategory === null,
        );

        // Build structured category list with subcategories
        const structured: any = parentCategories.map(parent => ({
          ...parent,
          parentCategory: parent.parentCategory ?? undefined,
          subcategories: allCategories.filter(
            sub => sub.parentCategory === parent.id,
          ),
        }));

        setCategories(structured);

        // Simulate delay before showing content
        setTimeout(() => {
          setLoading(false);
        }, 1000);
      });

    return () => unsubscribe(); // Cleanup Firestore listener
  }, []);

  // Reset selection when returning to this screen
  useFocusEffect(
    useCallback(() => {
      return () => {
        setSelecteIds([]);
        setSelectedParentCatgory(null);
        setSelectedSubcategories([]);
      };
    }, []),
  );

  // Filter only selected parent category if any
  const filteredCategories = useMemo(() => {
    return selectedParentCategory
      ? categories.filter(cat => cat.id === selectedParentCategory.id)
      : categories;
  }, [categories, selectedParentCategory]);

  // Handle accordion open/close
  const handleCategoryOpen = (category: parentCatProps) => {
    if (selectedParentCategory?.id === category.id) {
      setSelectedParentCatgory(null);
      setSelectedSubcategories([]);
      setSelecteIds([]);
    } else {
      setSelectedParentCatgory(category);
      setSelectedSubcategories([]);
      setSelecteIds([category.id]);
    }
  };

  // Handle subcategory select/deselect
  const handleSubcategorySelect = (cat: subCatProps) => {
    const isSelected = selectedSubcategories?.some(sub => sub.id === cat.id);

    if (isSelected) {
      setSelectedSubcategories(prevSelected =>
        prevSelected.filter(sub => sub.id !== cat.id),
      );
      setSelecteIds(prev => prev.filter(id => id != cat.id));
    } else {
      setSelectedSubcategories(prevSelected => [...prevSelected, cat]);
      setSelecteIds(prev => [...prev, cat.id]);
    }
  };

  return (
    <View style={styles.container}>
      {/* Progress indicator */}
      <ProgressBar value={1} max={2} />

      {/* Screen header */}
      <Text style={[appStyles.h2, {paddingTop: 14}]}>Choose your category</Text>
      <Text style={[appStyles.body4, {paddingTop: 8}]}>
        Lorem ipsum dolor sit amet consectetur adipisicing elit. Accusantium
        similique consectetur excepturi dolorem dolore voluptatibus, vero,
        cupiditate est nesciunt incidunt
      </Text>

      {/* Show loader or category list */}
      {loading ? (
        <ActivityIndicator
          size={'large'}
          color={Colors.buttonbgcolor}
          style={{marginTop: 100}}
        />
      ) : (
        <>
          {/* Category List */}
          <FlatList
            data={filteredCategories}
            contentContainerStyle={{marginTop: 20}}
            renderItem={({item}) => {
              return (
                <CategoryAccordion
                  key={item.id}
                  category={item}
                  isOpen={selectedParentCategory?.id === item.id}
                  onPressAccordion={() =>
                    handleCategoryOpen({
                      id: item.id,
                      parentCategoryName: item.title,
                      url: item.icon.url,
                    })
                  }
                  selectedSubcategories={selectedSubcategories}
                  onSubcategorySelect={handleSubcategorySelect}
                />
              );
            }}
            ListFooterComponent={
              <Text style={[appStyles.body4, styles.paragraph]}>
                We use this information to calculate and provide you with daily
                personalized recommendations.
              </Text>
            }
          />

          {/* Continue button */}
          <Button
            disabled={selectedSubcategories.length === 0 || loading}
            title="Create Task"
            containerStyle={{marginBottom: 14, marginTop: 10}}
            onPress={() =>
              navigation.navigate('SelectAttachment', {
                selectedCatIds: selectedIds,
                category: {
                  parentCategory: {
                    title: selectedParentCategory?.parentCategoryName || '',
                    url: selectedParentCategory?.url || '',
                  },
                  subCategories: selectedSubcategories.map(
                    cat => cat.subCategoryName,
                  ),
                },
              })
            }
          />
        </>
      )}
    </View>
  );
};

export default SelectCategory;

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 16,
  },
  paragraph: {
    color: '#2C2C2E99',
    textAlign: 'center',
    marginTop: 26,
  },
});
