import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {useEffect, useState} from 'react';
import {
  Text,
  View,
  ScrollView,
  ActivityIndicator,
  TouchableOpacity,
  StyleSheet,
  Image,
} from 'react-native';
import moment, {Moment} from 'moment';
import {ITask} from '../../../interfaces/ITask';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {OngoingEmptyState} from '../../../assets/svgIcons';
import {GestureHandlerRootView, Swipeable} from 'react-native-gesture-handler';
import StripeCalendar from '../../../component/common/StripeCalendar/StripeCalendar';
import firestore from '@react-native-firebase/firestore';
import {BottomTabParamlist} from '../../../navigation/ClientBottomTabs';
import {IUser} from '../../../interfaces/IUser';
import CalendarItem from '../../../component/common/CalendarItem/CalendarItem';

type Props = NativeStackScreenProps<BottomTabParamlist, 'Calendar'>;

const Calendar: React.FC<Props> = ({navigation}) => {
  const [selectedDate, setSelectedDate] = useState<Date>(moment().toDate());
  const [tasks, setTasks] = useState<ITask[]>([]);
  const [isLoading, setLoading] = useState(false);
  const [staffUser, setStaffUser] = useState<IUser | null>(null);

  const fetchTasksByDate = async () => {
    // setLoading(true);
    // Format selectedDate to moment for easy manipulation
    const day = moment(selectedDate);

    // Start of the day (00:00:00.000)
    const startOfDay = day.startOf('day').toDate();

    // End of the day (23:59:59.999)
    const endOfDay = day.endOf('day').toDate();

    try {
      // Firestore query: get tasks where durationTimeStamp is between start and end of selected day
      const snapshot = await firestore()
        .collection('Tasks')
        .where(
          'durationTimeStamp',
          '>=',
          firestore.Timestamp.fromDate(startOfDay),
        )
        .where(
          'durationTimeStamp',
          '<=',
          firestore.Timestamp.fromDate(endOfDay),
        )
        .get();

      // Map Firestore docs to array of tasks
      const tasks = snapshot.docs.map(
        doc =>
          ({
            id: doc.id,
            ...doc.data(),
          } as ITask),
      );
      setTasks(tasks);
    } catch (error) {
      console.error('Error fetching tasks by date:', error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTasksByDate();
  }, [selectedDate]);

  const renderTaskList = (tasks: ITask[], title: string) => (
    <>
      {tasks.length ? (
        <>
          <Text style={styles.sectionHeader}>{title}</Text>
          {tasks.map(item => {
            return (
              <CalendarItem
                title={item.title}
                taskDueDate={moment(item.durationTimeStamp.toDate()).format(
                  'MMMM D.YYYY',
                )}
                key={item.id}
                staffId={item.staffId}
                onPressCalendarItem={(staffUser?: IUser | null) =>
                  item &&
                  navigation.navigate('TaskDetails', {
                    taskDetails: item,
                    staffUser: staffUser || undefined,
                  })
                }
              />
            );
          })}
        </>
      ) : (
        <View style={styles.emptyContainer}>
          <OngoingEmptyState height={130} />
          <Text style={styles.emptyText}>No tasks on this day</Text>
        </View>
      )}
    </>
  );

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <View style={styles.container}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollViewContent}>
          <StripeCalendar
            selectedDate={selectedDate}
            onDateSelected={date => setSelectedDate(date)}
          />
          {isLoading ? (
            <ActivityIndicator
              size={'large'}
              color={Colors.buttonbgcolor}
              style={{alignItems: 'center', marginTop: 100}}
            />
          ) : (
            <View style={styles.innerContainer}>
              {renderTaskList(tasks || [], 'Today’s Task')}
            </View>
          )}
        </ScrollView>
      </View>
    </GestureHandlerRootView>
  );
};

export default Calendar;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollViewContent: {
    paddingBottom: 30,
  },
  sectionHeader: {
    marginVertical: 12,
    fontFamily: Fonts.Semibold,
    fontSize: 12,
    color: Colors.primary,
    marginLeft: 20,
  },
  innerContainer: {
    // paddingHorizontal: 20,
  },
  emptyText: {
    textAlign: 'center',
    fontFamily: Fonts.Semibold,
    color: Colors.placeholder,
    marginTop: 10,
    fontSize: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    marginTop: 40,
  },
});
