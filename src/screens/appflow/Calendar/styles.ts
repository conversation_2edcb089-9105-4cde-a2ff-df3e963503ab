import {StyleSheet} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {s, vs, ms} from 'react-native-size-matters';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollViewContent: {
    paddingBottom: vs(30),
  },
  calendarStrip: {
    height: ms(140),
    backgroundColor: Colors.background,
  },
  dayContainer: {
    backgroundColor: Colors.white,
    marginLeft: s(15),
    height: ms(69),
    width: ms(45),
    borderRadius: 12,
  },
  dateName: {
    fontSize: ms(9.5),
    color: Colors.primary,
    fontFamily: Fonts.Medium,
  },
  dateNumber: {
    fontSize: ms(16),
    color: Colors.primary,
    lineHeight: 22,
    fontFamily: Fonts.Semibold,
    marginTop: vs(4),
  },
  highlightDateContainer: {
    backgroundColor: Colors.buttonbgcolor,
    borderRadius: 12,
  },
  highlightDateNumber: {
    color: Colors.white,
  },
  highlightDateName: {
    color: Colors.white,
  },
  calendarRightIcon: {
    position: 'absolute',
    top: -90,
    right: -1000,
    tintColor: Colors.background,
  },
  calendarLeftIcon: {
    position: 'absolute',
    top: -90,
    right: -1000,
    tintColor: Colors.background,
  },
  calendarHeaderContainer: {
    textAlign: 'left',
    alignSelf: 'flex-start',
    marginLeft: s(20),
  },
  calendarHeader: {
    fontSize: ms(16),
    fontFamily: Fonts.Semibold,
    lineHeight: 27,
    color: Colors.primary,
  },
  sectionHeader: {
    marginTop: vs(10),
    fontFamily: Fonts.Semibold,
    fontSize: ms(12),
    lineHeight: 18,
    color: Colors.primary,
    marginLeft: 20,
  },
  innerContainer: {
    // paddingHorizontal: 20,
  },
  emptyText: {
    textAlign: 'center',
    fontFamily: Fonts.Semibold,
    color: Colors.placeholder,
    marginTop: vs(10),
    fontSize: ms(12),
  },
  rightActionContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: ms(60),
    backgroundColor: '#55AB67',
    marginTop: 12,
    marginLeft: -20,
  },
  taskContainer: {width: '91%', alignSelf: 'center'},
  emptyContainer: {alignItems: 'center', marginTop: 40},
});
