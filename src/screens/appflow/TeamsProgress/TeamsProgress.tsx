import {
  FlatList,
  Image,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  StyleSheet,
  Dimensions,
} from 'react-native';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {IUser} from '../../../interfaces/IUser';
import firestore from '@react-native-firebase/firestore';
import {AuthContext} from '../../../../App';
import {BottomTabParamlist} from '../../../navigation/ClientBottomTabs';
import {CircularProgressBase} from 'react-native-circular-progress-indicator';
import {images} from '../../../assets/images';
import {ITask} from '../../../interfaces/ITask';
import {getCompletionPercentage} from '../../../helpers/getCompletionPercentage';

type Props = NativeStackScreenProps<BottomTabParamlist, 'TeamsProgress'>;
const TeamsProgress: React.FC<Props> = ({navigation}) => {
  const {userData, userId} = useContext(AuthContext);
  const [staffUsers, setStaffUsers] = useState<IUser[]>();
  const [staffLoading, setStaffLoading] = useState(true);
  const [tasksLoading, setTaskLoading] = useState(true);
  const [tasks, setTasks] = useState<ITask[]>([]);

  const fetchManagerTasks = async () => {
    try {
      const snapShot = await firestore()
        .collection('Tasks')
        .where('managerId', '==', userId)
        .get();
      const tasksData: ITask[] = snapShot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
        } as ITask;
      });
      setTasks(tasksData);
    } catch (error) {
      console.log('Error fetching tasks', error);
    } finally {
      setTaskLoading(false);
    }
  };
  const getStaffUsers = async () => {
    try {
      const snapShot = await firestore()
        .collection('Users')
        .where('managerId', '==', userId)
        .get();
      const users: IUser[] = snapShot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
        } as IUser;
      });
      setStaffUsers(users);
    } catch (error) {
      console.log('error----', error);
    } finally {
      setStaffLoading(false);
    }
  };

  useEffect(() => {
    getStaffUsers();
  }, []);

  useEffect(() => {
    fetchManagerTasks();
  }, []);
  const circularProgressRef = useRef(null);

  const completedTask = useMemo(
    () => tasks.filter(task => task.status === 'completed'),
    [tasks],
  );
  const inProgressOrReviewingCount = useMemo(() => {
    if (tasks.length === 0) return 0;
    return tasks.filter(
      task => task.status === 'progress' || task.status === 'reviewing',
    ).length;
  }, [tasks]);

  const renderItem = useCallback(
    ({item}: {item: IUser}) => {
      const assignedTasks = tasks.filter(t => t.staffId === item.id);
      const completedTasks = assignedTasks.filter(
        t => t.status === 'completed',
      );

      return (
        <TouchableOpacity
          style={{marginBottom: 16, alignItems: 'center'}}
          activeOpacity={0.5}
          onPress={() =>
            navigation.navigate('ProgressDetails', {
              userData: item,
              tasks: assignedTasks,
            })
          }>
          <CircularProgressBase
            initialValue={1}
            ref={circularProgressRef}
            activeStrokeWidth={2.5}
            inActiveStrokeWidth={2.5}
            inActiveStrokeOpacity={0.2}
            value={completedTasks.length || 0}
            maxValue={assignedTasks.length || 1}
            radius={Dimensions.get('screen').width * 0.092}
            activeStrokeColor={Colors.buttonbgcolor}
            inActiveStrokeColor={'#CDCDCD'}
            strokeLinecap={'round'}>
            <Image
              source={
                item.profileImage?.url
                  ? {uri: item.profileImage.url}
                  : images.avatarPlaceholder
              }
              style={styles.avatarSmall}
            />
          </CircularProgressBase>
          <Text numberOfLines={1} style={styles.teamMemberName}>
            {item.name}
          </Text>
          <Text style={styles.teamMemberProgress}>
            {getCompletionPercentage(item.id, tasks)}%
          </Text>
        </TouchableOpacity>
      );
    },
    [tasks, staffUsers],
  );

  return (
    <View style={styles.container}>
      {staffLoading || tasksLoading ? (
        <ActivityIndicator
          size={'large'}
          color={Colors.buttonbgcolor}
          style={{alignSelf: 'center', marginTop: 100}}
        />
      ) : (
        <View style={styles.circularProgressContainer}>
          <View style={{alignItems: 'center'}}>
            <CircularProgressBase
              initialValue={1}
              ref={circularProgressRef}
              activeStrokeWidth={4}
              inActiveStrokeWidth={4}
              inActiveStrokeOpacity={0.2}
              value={completedTask.length || 0}
              maxValue={tasks.length}
              radius={50}
              activeStrokeColor={Colors.buttonbgcolor}
              inActiveStrokeColor={'#CDCDCD'}
              strokeLinecap={'round'}>
              <Image
                source={
                  userData.profileImage?.url
                    ? {uri: userData.profileImage.url}
                    : images.avatarPlaceholder
                }
                style={styles.avatarLarge}
              />
            </CircularProgressBase>
            <Text style={styles.userName}>{userData.name}</Text>
            <Text style={styles.taskInfo}>
              Completed{' '}
              <Text style={styles.taskInfoHighlight}>
                {completedTask.length}
              </Text>{' '}
              Pending{' '}
              <Text style={styles.taskInfoHighlight}>
                {inProgressOrReviewingCount}
              </Text>{' '}
            </Text>
          </View>

          <Text style={styles.myTeam}>My Team</Text>
          <FlatList<IUser>
            data={staffUsers}
            numColumns={4}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}
            columnWrapperStyle={{gap: 20}}
            contentContainerStyle={{marginTop: 24, paddingBottom: 100}}
            renderItem={renderItem}
          />
        </View>
      )}
    </View>
  );
};

export default TeamsProgress;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: 20,
  },
  circularProgressContainer: {
    marginTop: 12,
  },
  avatarLarge: {
    height: 82,
    width: 82,
    borderRadius: 100,
  },

  userName: {
    color: Colors.buttonbgcolor,
    fontFamily: Fonts.Semibold,
    fontSize: 12,
    textAlign: 'center',
    marginTop: 12,
    textTransform: 'capitalize',
  },
  taskInfo: {
    fontSize: 10,
    fontFamily: Fonts.Semibold,
    color: '#A3A2A2',
    textAlign: 'center',
    marginTop: 3,
  },
  avatarSmall: {
    height: Dimensions.get('screen').width * 0.16,
    width: Dimensions.get('screen').width * 0.16,
    borderRadius: 500,
  },
  teamMemberName: {
    fontSize: 10,
    fontFamily: Fonts.Medium,
    lineHeight: 12,
    textAlign: 'center',
    marginTop: 6,
    color: Colors.primary,
    width: Dimensions.get('screen').width * 0.16,
  },
  teamMemberProgress: {
    fontSize: 10,
    fontFamily: Fonts.Medium,
    lineHeight: 12,
    textAlign: 'center',
    color: Colors.primary,
  },
  myTeam: {
    fontFamily: Fonts.Medium,
    fontSize: 16,
    lineHeight: 24,
    marginTop: 30,
    color: Colors.primary,
  },
  taskInfoHighlight: {
    color: '#828282',
  },
});
