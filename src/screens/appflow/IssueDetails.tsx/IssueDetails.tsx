import {Image, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {appStyles, Colors, Fonts, sizes} from '../../../utilities/theme/theme';
import moment from 'moment'; // For formatting dates
import AppSlider from '../../../component/common/AppSlider/AppSlider'; // Custom image slider
import {getColorByStatus} from '../../../helpers/getColorByStatus';
type Props = NativeStackScreenProps<HomeStackParamsList, 'IssueDetails'>;

const IssueDetails: React.FC<Props> = ({navigation, route}) => {
  const {issue} = route.params; // Destructuring issue data from route params

  return (
    <View style={styles.container}>
      {/* Status tag */}
      <View
        style={[
          styles.statusContainer,
          {backgroundColor: getColorByStatus(issue.status)},
        ]}>
        <Text style={styles.statusText}>{issue.status}</Text>
      </View>

      {/* Main heading */}
      <Text style={[appStyles.h4, {marginTop: 16}]}>
        {issue.categoryStructure?.parentCategory.title}
      </Text>

      {/* Date when the issue was created */}
      <Text style={styles.paragraph}>
        {moment(issue.createdAt).format('ddd DD MMM YYYY')}
      </Text>

      {/* Image slider for attached issue images */}
      <AppSlider
        images={issue.attachment.map(item => item.url).filter(url => url) || []}
      />

      {/* Description section */}
      <View style={[styles.discriptionContainer, {marginTop: 24}]}>
        <Text style={styles.title}>Description</Text>
        <Text style={styles.description}>{issue.note}</Text>
      </View>

      {/* Subcategory title with icon */}
      <View style={styles.categoryContainer}>
        <Image
          source={{uri: issue.categoryStructure?.parentCategory.url}}
          style={{width: 20, height: 20}}
        />
        <Text style={[appStyles.h4, {color: '#080C10'}]}>Sub Category</Text>
      </View>

      {/* List of subcategories */}
      <View style={[styles.discriptionContainer, {marginTop: 12}]}>
        {issue.categoryStructure?.subCategories.map((cat, index) => (
          <Text
            key={index} // Always add a key when rendering lists
            style={[
              styles.title,
              {
                marginBottom:
                  index ===
                  (issue.categoryStructure?.subCategories?.length ?? 0) - 1
                    ? 0
                    : 6, // Adds spacing between items except the last one
                paddingTop: 0,
              },
            ]}>
            {`${index + 1}.  ${cat}`}
          </Text>
        ))}
      </View>
    </View>
  );
};

export default IssueDetails;

// Styles for the component
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 8,
  },
  statusContainer: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 3,
    borderRadius: 4,
  },
  statusText: {
    color: 'black',
    fontFamily: Fonts.Medium,
    fontSize: 12,
    textTransform: 'capitalize',
  },
  paragraph: {
    fontSize: 14,
    color: '#475467',
    paddingTop: 2,
    fontFamily: Fonts.Medium,
  },
  discriptionContainer: {
    borderWidth: 1,
    borderColor: '#EAECF0',
    padding: 12,
    borderRadius: 12,
  },
  title: {
    fontSize: 12,
    color: Colors.primary_text,
    paddingTop: 2,
    fontFamily: Fonts.Semibold,
  },
  description: {
    fontSize: 10,
    color: '#475467',
    paddingTop: 4,
    fontFamily: Fonts.Medium,
  },
  categoryContainer: {
    marginTop: 24,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
});
