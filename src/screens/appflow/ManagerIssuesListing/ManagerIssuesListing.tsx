import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {Colors, Fonts, sizes} from '../../../utilities/theme/theme';
import Searchbar from '../../../component/Searchbar/Searchbar';
import {images} from '../../../assets/images';
import {AuthContext} from '../../../../App';
import {IIssue} from '../../../interfaces/IIssue';
import {getIssuesByManagerId} from '../../../backend/Issues';
import ManagerIssueCard from '../../../component/common/ManagerIssueCard/ManagerIssueCard';
import {useFocusEffect} from '@react-navigation/native';
import {getHoursLeft} from '../../../helpers/getHoursLeft';
type Props = NativeStackScreenProps<
  HomeStackParamsList,
  'ManagerIssuesListing'
>;

const ManagerIssuesListing: React.FC<Props> = ({navigation, route}) => {
  const {issueStatus} = route.params;
  const {userId} = useContext(AuthContext);
  const [issues, setIssues] = useState<IIssue[]>([]);
  const [loading, setIsLoading] = useState(true);
  const [searchText, setSearchText] = useState('');

  const capitalize = (text: string) =>
    text.replace(/\b\w/g, char => char.toUpperCase()); // Capitalize each word
  useEffect(() => {
    navigation.setOptions({
      headerTitle: capitalize(issueStatus),
      headerTitleStyle: styles.headerTitleStyle,
    });
  }, []);

  useFocusEffect(
    useCallback(() => {
      if (!userId) return;

      let isActive = true;

      const fetch = async () => {
        const issues = await getIssuesByManagerId(userId, issueStatus); // Add `limit` if needed
        if (isActive) {
          setIssues(issues);
          setIsLoading(false);
        }
      };

      fetch();

      return () => {
        isActive = false;
      };
    }, [userId, issueStatus]), // Include all dependencies
  );

  // Filtered list based on search input
  const filteredIssues = useMemo(() => {
    if (!searchText.trim()) return issues;
    const lowerSearch = searchText.toLowerCase();

    return issues.filter(
      issue =>
        issue.categoryStructure?.parentCategory.title
          ?.toLowerCase()
          .includes(lowerSearch) ||
        issue.note?.toLowerCase().includes(lowerSearch),
    );
  }, [searchText, issues]);

  return (
    <View style={styles.container}>
      <Searchbar
        RightIcon={images.searchicon}
        placeholder="Search Issue"
        showSearchFilter
        containerStyle={{marginBottom: 18}}
        value={searchText}
        onChangeText={setSearchText}
      />
      {loading ? (
        <ActivityIndicator
          size="large"
          color={Colors.buttonbgcolor}
          style={{marginTop: 100}}
        />
      ) : (
        <FlatList
          data={filteredIssues}
          contentContainerStyle={{gap: 12}}
          renderItem={({item, index}) => {
            return (
              <ManagerIssueCard
                title={item.categoryStructure?.parentCategory.title || ''}
                note={item.note}
                status={issueStatus}
                priority={item.priority}
                timeLeft={
                  item?.durationTimeStamp &&
                  getHoursLeft(item?.durationTimeStamp).toString() +
                    ' hours Left'
                }
                dueDate={item.createdAt}
                assignedStaff={item.assignedStaff}
                onViewIssueDetails={() =>
                  navigation.navigate('IssueTasks', {issue: item})
                }
                issueId={item.id}
              />
            );
          }}
          ListEmptyComponent={
            <Text style={styles.emptyText}>No {issueStatus} issues found.</Text>
          }
        />
      )}
    </View>
  );
};

export default ManagerIssuesListing;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 12,
  },
  headerTitleStyle: {
    fontSize: 12,
    color: Colors.primary,
    fontFamily: Fonts.Medium,
  },
  emptyText: {
    textAlign: 'center',
    color: Colors.lightcolor,
    marginTop: 40,
    fontSize: 16,
  },
});
