import React, {FC, useEffect, useMemo, useRef} from 'react';
import {Text, View, Image, ScrollView, SectionList} from 'react-native';
import {Colors} from '../../../utilities/theme/theme';
import {ms} from 'react-native-size-matters';
import {CircularProgressBase} from 'react-native-circular-progress-indicator';
import {images} from '../../../assets/images';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {styles} from './styles';
import moment from 'moment';
import {OngoingEmptyState} from '../../../assets/svgIcons';
import {getCompletionPercentage} from '../../../helpers/getCompletionPercentage';
import CalendarItem from '../../../component/common/CalendarItem/CalendarItem';
import {IUser} from '../../../interfaces/IUser';

type Props = NativeStackScreenProps<HomeStackParamsList, 'ProgressDetails'>;

const ProgressDetails: FC<Props> = ({navigation, route}) => {
  const circularProgressRef = useRef(null);
  const {userData, tasks} = route.params;

  const today = moment().startOf('day');

  const taskSections = useMemo(() => {
    const todayTasks = tasks.filter(task =>
      moment(task.durationTimeStamp.toDate()).isSame(today, 'day'),
    );

    const completedTasks = tasks.filter(task => task.status === 'completed');

    const pendingTasks = tasks.filter(
      task => task.status === 'progress' || task.status === 'reviewing',
    );

    const sections = [];

    if (todayTasks.length > 0) {
      sections.push({title: "Today's Tasks", data: todayTasks});
    }

    if (completedTasks.length > 0) {
      sections.push({title: 'Completed Tasks', data: completedTasks});
    }

    if (pendingTasks.length > 0) {
      sections.push({title: 'Pending Tasks', data: pendingTasks});
    }

    return sections;
  }, [tasks]);

  const completedTask = useMemo(
    () => tasks.filter(task => task.status === 'completed'),
    [tasks],
  );

  const inProgressOrReviewingCount = useMemo(() => {
    if (tasks.length === 0) return 0;
    return tasks.filter(
      task => task.status === 'progress' || task.status === 'reviewing',
    ).length;
  }, [tasks]);

  useEffect(() => {
    navigation.setOptions({headerTitle: userData.name});
  }, []);

  return (
    <View style={styles.container}>
      <SectionList
        sections={taskSections}
        ListHeaderComponent={
          <View>
            <View style={styles.circularProgressContainer}>
              <CircularProgressBase
                initialValue={1}
                ref={circularProgressRef}
                activeStrokeWidth={4}
                inActiveStrokeWidth={4}
                inActiveStrokeOpacity={0.2}
                value={completedTask.length || 0}
                maxValue={tasks.length}
                radius={ms(50)}
                activeStrokeColor={Colors.buttonbgcolor}
                inActiveStrokeColor="#CDCDCD"
                strokeLinecap="round"
                // maxValue={100}
              >
                <Image
                  source={
                    userData.profileImage?.url
                      ? {uri: userData.profileImage.url}
                      : images.avatarPlaceholder
                  }
                  style={styles.avatarLarge}
                />
              </CircularProgressBase>
            </View>
            <View style={styles.userInfoContainer}>
              <Text style={styles.userName}>{userData.name}</Text>
            </View>
            {/* Completed task */}
            <Text style={styles.taskInfo}>
              Completed{' '}
              <Text style={styles.taskInfoHighlight}>
                {completedTask.length || 0}
              </Text>{' '}
              {/* Pending task */}
              pending{' '}
              <Text style={styles.taskInfoHighlight}>
                {inProgressOrReviewingCount}
              </Text>{' '}
            </Text>
            <Text style={styles.progressPercentage}>
              {getCompletionPercentage(userData.id, tasks)}
              <Text style={styles.progressPercentageSymbol}> %</Text>
            </Text>
          </View>
        }
        keyExtractor={(item, index) => item.id + index}
        renderSectionHeader={({section: {title}}) => (
          <Text style={[styles.sectionHeader, {marginTop: 0}]}>{title}</Text>
        )}
        renderItem={({item}) => (
          <CalendarItem
            containerStyle={{marginBottom: 0}}
            title={item.title}
            taskDueDate={moment(item.durationTimeStamp.toDate()).format(
              'MMMM D, YYYY',
            )}
            staffId={item.staffId}
            onPressCalendarItem={(staffUser?: IUser | null) =>
              navigation.navigate('TaskDetails', {
                taskDetails: item,
                staffUser: staffUser || undefined,
              })
            }
          />
        )}
        contentContainerStyle={{paddingBottom: 40, gap: 10}}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <OngoingEmptyState height={130} />
            <Text style={styles.emptyText}>No tasks added yet</Text>
          </View>
        )}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default ProgressDetails;
