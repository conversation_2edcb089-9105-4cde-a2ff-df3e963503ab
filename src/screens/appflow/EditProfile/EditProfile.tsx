import React, {useContext, useState} from 'react';
import {Text, StyleSheet, View, Image, TouchableOpacity} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {images} from '../../../assets/images';
import Listitems from '../../../component/Listitems/Listitems';
import {Button} from '../../../component/Button/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import Input from '../../../component/Input/Input';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {AuthContext} from '../../../../App';
import firestore from '@react-native-firebase/firestore';
import ImageCropPicker from 'react-native-image-crop-picker';
import storage from '@react-native-firebase/storage';
import {ms} from 'react-native-size-matters';

type Props = NativeStackScreenProps<HomeStackParamsList, 'EditProfile'>;

const EditProfile: React.FC<Props> = ({navigation}) => {
  const {userData, userId, setUserData} = useContext(AuthContext);
  const [isLoading, setIsLoading] = useState(false);

  const validationSchema = Yup.object().shape({
    name: Yup.string().required('Name is required'),
    profilePic: Yup.string().required('Profile picture is required'),
  });

  const formik = useFormik({
    initialValues: {
      name: userData?.name || '',
      profilePic: userData?.profileImage?.url || '',
    },
    validationSchema: validationSchema,
    enableReinitialize: true,
    validateOnMount: true,
    onSubmit: values => {
      updateProfile(values.name, values.profilePic);
    },
  });

  const updateProfile = async (name: string, profilePic: string) => {
    try {
      setIsLoading(true);
      let url;
      let referenceData;
      if (profilePic !== userData?.profileImage?.url) {
        referenceData = await uploadImage(profilePic);

        url = await referenceData?.getDownloadURL();
      }

      await firestore()
        .collection('Users')
        .doc(userId)
        .update({
          name: formik.values.name,
          profileImage:
            profilePic !== userData?.profileImage?.url
              ? {
                  url: url,
                  ref: referenceData?.fullPath,
                }
              : userData.profileImage,
        });

      setUserData({
        ...userData,
        name,
        profileImage: {
          url: url || userData?.profileImage?.url || '',
          ref: referenceData?.fullPath || userData?.profileImage?.ref || '',
        },
      });

      navigation.goBack();
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImagePicker = () => {
    ImageCropPicker.openPicker({
      cropping: true,
      multiple: false,
    })
      .then(image => {
        formik.setFieldValue('profilePic', image.path);
      })
      .catch(error => {
        console.log('Image picker error:', error);
      });
  };

  const uploadImage = async (profilePicPath: string) => {
    try {
      const filePath = `users/${userId}/profileImages/${userId}_img`;
      const reference = storage().ref(filePath);

      await reference.putFile(profilePicPath);
      return reference;
    } catch (error) {
      console.log('Image upload error:', error);
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Image
        style={styles.profileContainer}
        source={
          formik.values.profilePic
            ? {uri: formik.values.profilePic}
            : images.avatarPlaceholder
        }
      />
      <TouchableOpacity
        onPress={handleImagePicker}
        style={{position: 'absolute', top: ms(76), left: ms(200)}}>
        <Image style={styles.addimgicon} source={images.addimgicon} />
      </TouchableOpacity>

      <Input
        leftIcon={images.usericonP}
        RightIcon={images.editiconP}
        containerStyle={{marginTop: 72}}
        value={formik.values.name}
        onChangeText={formik.handleChange('name')}
        onBlur={formik.handleBlur('name')}
      />
      {formik.touched.name && formik.errors.name ? (
        <Text style={styles.errorText}>{formik.errors.name}</Text>
      ) : null}

      <Listitems leftIcon={images.emailicon} title={userData?.email} />

      <View style={{marginTop: 160}}>
        <Button
          title="Save"
          onPress={formik.handleSubmit}
          isLoading={isLoading}
          disabled={isLoading || !formik.isValid}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: 24,
  },
  profileContainer: {
    width: 90,
    height: 90,
    alignSelf: 'center',
    marginTop: 12,
    borderColor: Colors.buttonbgcolor,
    borderWidth: 3,
    borderRadius: 50,
  },
  nameStyle: {
    color: Colors.primary,
    alignSelf: 'center',
    fontSize: 14,
    fontFamily: Fonts.Medium,
    marginTop: 10,
  },
  emailStyle: {
    color: Colors.buttonbgcolor,
    alignSelf: 'center',
    fontSize: 8,
    fontFamily: Fonts.Regular,
    lineHeight: 12,
    marginTop: 4,
  },
  addimgicon: {
    width: 22,
    height: 22,
  },
  errorText: {
    color: Colors.red,
    fontSize: 12,
    marginTop: 5,
  },
});

export default EditProfile;
