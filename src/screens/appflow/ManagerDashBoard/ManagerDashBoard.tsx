import {
  ActivityIndicator,
  FlatList,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useContext, useEffect, useMemo, useState} from 'react';
import {appStyles, Colors, Fonts, sizes} from '../../../utilities/theme/theme';
import {AuthContext} from '../../../../App';
import {images} from '../../../assets/images';
import Searchbar from '../../../component/Searchbar/Searchbar';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import ClientIssueCard from '../../../component/ClientIssueCard/ClientIssueCard';
import {
  CheckCircle,
  ReviewIcon,
  StackIcon,
  SunIcon,
} from '../../../assets/svgIcons';
import ManagerWidget from '../../../component/ManagerWidget/ManagerWidget';
import CustomHeader from '../../../component/CustomHeader/CustomHeader';
import {IIssue} from '../../../interfaces/IIssue';
import {getIssuesByManagerId} from '../../../backend/Issues';
import {getHoursLeft} from '../../../helpers/getHoursLeft';
import moment from 'moment';

type Props = NativeStackScreenProps<HomeStackParamsList, 'ManagerDashBoard'>;

const ManagerDashBoard: React.FC<Props> = ({navigation}) => {
  const {userData, userId} = useContext(AuthContext);

  const [issues, setIssues] = useState<IIssue[]>([]);
  const [loading, setIsLoading] = useState(true);
  const [searchText, setSearchText] = useState('');

  // Filtered list based on search input
  // const filteredIssues = useMemo(() => {
  //   if (!searchText.trim()) return pendingIssues;
  //   const lowerSearch = searchText.toLowerCase();

  //   return pendingIssues.filter(
  //     issue =>
  //       issue.categoryStructure?.parentCategory.title
  //         ?.toLowerCase()
  //         .includes(lowerSearch) ||
  //       issue.note?.toLowerCase().includes(lowerSearch),
  //   );
  // }, [searchText, pendingIssues]);
  useEffect(() => {
    console.log('userId', userId);
    if (!userId) return;
    setIsLoading(true);

    const fetch = async () => {
      try {
        const issuesAll = await getIssuesByManagerId(userId);
        setIssues(issuesAll);
      } catch (error) {
        console.log('Error fetching issues', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetch();
  }, [userId]); // Include all dependencies

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={Colors.background} />
      <CustomHeader
        onPressNotification={() => navigation.navigate('Notification')}
        onPressProfile={() => navigation.navigate('Profile')}
      />

      <Searchbar
        placeholder={'Search Issue'}
        value={searchText}
        onChangeText={text => setSearchText(text)}
        RightIcon={images.searchicon}
        containerStyle={styles.searchContainer}
        showSearchFilter
      />
      <View style={styles.rowContainer}>
        <Text style={appStyles.h6}>Ongoing Tickets</Text>
        {/* <Text style={appStyles.body5}>See all</Text> */}
      </View>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 16}}>
        {/* TASK LIST */}
        {loading ? (
          <ActivityIndicator size="large" color={Colors.buttonbgcolor} />
        ) : (
          <View>
            <FlatList
              data={issues}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={{
                gap: 10,
                marginTop: 12,
                paddingHorizontal: sizes.paddingHorizontal,
              }}
              renderItem={({item}) => {
                return (
                  <ClientIssueCard
                    timeLeft={
                      item?.durationTimeStamp
                        ? getHoursLeft(item?.durationTimeStamp).toString() +
                          ' Hours Left'
                        : ''
                    }
                    priority={item.priority || ''}
                    status={item.status}
                    title={item.categoryStructure?.parentCategory?.title || ''}
                    commentsCount={2}
                    attachmentsCount={
                      item.attachment.length > 0 ? item.attachment.length : 0
                    }
                    time={
                      item.createdAt
                        ? moment(item.createdAt.toDate())
                            .format('hh:mm A')
                            .toUpperCase()
                        : ''
                    }
                    assignedStaff={item.assignedStaff}
                    issueId={item.id}
                    onPress={() =>
                      navigation.navigate('IssueTasks', {issue: item})
                    }
                  />
                );
              }}
            />
          </View>
        )}
        <View style={styles.spaceBetContainer}>
          <Text style={appStyles.h6}>Tickets Overview</Text>
          {/* <Text style={appStyles.body5}>See all</Text> */}
        </View>
        <View style={[appStyles.flexSpaceBetween, styles.row, {marginTop: 12}]}>
          <ManagerWidget
            icon={<SunIcon />}
            title="To Do"
            taskInfo="10 tasks Mar 30"
            onPress={() => navigation.navigate('ManagerTodoIssue')}
            status={'todo'}
          />
          <ManagerWidget
            icon={<StackIcon />}
            title="In Progress"
            taskInfo="10 tasks Mar 30"
            onPress={() =>
              navigation.navigate('ManagerIssuesListing', {
                issueStatus: 'progress',
              })
            }
            status={'progress'}
          />
        </View>
        <View style={[appStyles.flexSpaceBetween, styles.row]}>
          <ManagerWidget
            icon={<ReviewIcon />}
            title="Reviewing"
            taskInfo="10 tasks Mar 30"
            onPress={() =>
              navigation.navigate('ManagerIssuesListing', {
                issueStatus: 'reviewing',
              })
            }
            status={'reviewing'}
          />
          <ManagerWidget
            icon={<CheckCircle />}
            title="Complete"
            taskInfo="10 tasks Mar 30"
            onPress={() =>
              navigation.navigate('ManagerIssuesListing', {
                issueStatus: 'completed',
              })
            }
            status={'completed'}
          />
        </View>
      </ScrollView>
    </View>
  );
};

export default ManagerDashBoard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? 16 : 76,
  },
  searchContainer: {
    marginTop: 24,
    borderRadius: sizes.borderRadius,
    marginHorizontal: sizes.paddingHorizontal,
  },
  rowContainer: {
    marginTop: 18,
    paddingHorizontal: sizes.paddingHorizontal,
  },
  row: {
    paddingHorizontal: sizes.paddingHorizontal,
    marginBottom: 13,
  },
  spaceBetContainer: {
    marginTop: 16,
    paddingHorizontal: sizes.paddingHorizontal,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  emptyText: {
    textAlign: 'center',
    fontFamily: Fonts.Semibold,
    color: Colors.placeholder,
    marginTop: 16,
    fontSize: 12,
  },
});
