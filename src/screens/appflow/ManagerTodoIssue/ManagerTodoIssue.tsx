import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {
  useContext,
  useEffect,
  useState,
  useMemo,
  useCallback,
} from 'react';
import {Colors, sizes} from '../../../utilities/theme/theme';
import Searchbar from '../../../component/Searchbar/Searchbar';
import {images} from '../../../assets/images';
import IssueListItem from '../../../component/IssueListItem/IssueListItem';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthContext} from '../../../../App';
import {IIssue} from '../../../interfaces/IIssue';
import moment from 'moment';
import {getIssuesByManagerId} from '../../../backend/Issues';
import {useFocusEffect} from '@react-navigation/native';

type Props = NativeStackScreenProps<HomeStackParamsList, 'ManagerTodoIssue'>;

const ManagerTodoIssue: React.FC<Props> = ({navigation}) => {
  const {userId} = useContext(AuthContext);
  const [issue, setIssues] = useState<IIssue[]>([]);
  const [loading, setIsLoading] = useState(true);
  const [searchText, setSearchText] = useState('');

  useFocusEffect(
    useCallback(() => {
      if (!userId) return;

      let isActive = true;

      const fetch = async () => {
        const issues = await getIssuesByManagerId(userId, 'todo'); // Add `limit` if needed
        if (isActive) {
          setIssues(issues);
          setIsLoading(false);
        }
      };

      fetch();

      return () => {
        isActive = false;
      };
    }, [userId]), // Include all dependencies
  );

  // Filtered list based on search input
  const filteredIssues = useMemo(() => {
    if (!searchText.trim()) return issue;
    const lowerSearch = searchText.toLowerCase();

    return issue.filter(
      issue =>
        issue.categoryStructure?.parentCategory.title
          ?.toLowerCase()
          .includes(lowerSearch) ||
        issue.note?.toLowerCase().includes(lowerSearch),
    );
  }, [searchText, issue]);

  return (
    <View style={styles.container}>
      <Searchbar
        RightIcon={images.searchicon}
        placeholder="Search Issue"
        showSearchFilter
        value={searchText}
        onChangeText={setSearchText}
        containerStyle={{marginBottom: 18}}
      />

      {loading ? (
        <ActivityIndicator
          size="large"
          color={Colors.buttonbgcolor}
          style={{marginTop: 100}}
        />
      ) : (
        <FlatList
          data={filteredIssues}
          keyExtractor={item => item.id}
          contentContainerStyle={{gap: 12, paddingBottom: 12}}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={
            <Text style={styles.emptyText}>No issues found.</Text>
          }
          renderItem={({item}) => (
            <IssueListItem
              status={'todo'}
              title={item.categoryStructure?.parentCategory.title || ''}
              note={item.note}
              date={moment(item.createdAt).format('ddd DD MMM YYYY')}
              onViewDetails={() =>
                navigation.navigate('IssueTasks', {issue: item})
              }
            />
          )}
        />
      )}
    </View>
  );
};

export default ManagerTodoIssue;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 12,
  },
  emptyText: {
    textAlign: 'center',
    color: Colors.lightcolor,
    marginTop: 40,
    fontSize: 16,
  },
});
