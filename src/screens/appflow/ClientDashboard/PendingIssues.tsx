import React from 'react';
import {FlatList, Text} from 'react-native';
import moment from 'moment';
import {IIssue} from '../../../interfaces/IIssue';
import IssueListItem from '../../../component/IssueListItem/IssueListItem';
import {appStyles} from '../../../utilities/theme/theme';

interface Props {
  issues: IIssue[];
  navigation: any;
}

const PendingIssues: React.FC<Props> = ({issues, navigation}) => {
  return (
    <FlatList
      data={issues}
      contentContainerStyle={{marginTop: 12, gap: 12, paddingBottom: 42}}
      showsVerticalScrollIndicator={false}
      ListEmptyComponent={
        <Text style={appStyles.emptyStateText}>No Progress issue found</Text>
      }
      renderItem={({item}) => (
        <IssueListItem
          status={item.status}
          title={item.categoryStructure?.parentCategory.title || ''}
          note={item.note}
          date={moment(item.createdAt).format('ddd DD MMM YYYY')}
          onViewDetails={() =>
            navigation.navigate('IssueDetails', {issue: item})
          }
        />
      )}
    />
  );
};

export default PendingIssues;
