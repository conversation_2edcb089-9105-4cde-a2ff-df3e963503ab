import {
  ActivityIndicator,
  Platform,
  StatusBar,
  StyleSheet,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Colors, sizes} from '../../../utilities/theme/theme';
import {images} from '../../../assets/images';
import useIssue from '../../../hooks/useIssue';
import Searchbar from '../../../component/Searchbar/Searchbar';
import {IIssue} from '../../../interfaces/IIssue';
import AllIssues from './AllIssues';
import PendingIssues from './PendingIssues';
import CompletedIssues from './CompletedIssues';
import AnimatedTabs from '../../../component/common/AnimatedTabs/AnimatedTabs';
import CustomHeader from '../../../component/CustomHeader/CustomHeader';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
type Props = NativeStackScreenProps<HomeStackParamsList, 'ClientDashboard'>;

const ClientDashboard: React.FC<Props> = ({navigation}) => {
  const [activeTab, setActiveTab] = useState<
    'In Progress' | 'Pending' | 'Completed'
  >('Pending');
  const {pendingIssues, progressIssues, completedIssues, issueLoading} =
    useIssue();
  const [searchText, setSearchText] = useState('');
  const [filteredIssues, setFilteredIssues] = useState<IIssue[]>();

  const getCurrentIssues = () => {
    if (activeTab === 'Pending') return pendingIssues;
    if (activeTab === 'In Progress') return progressIssues;
    return completedIssues;
  };
  useEffect(() => {
    const issues = getCurrentIssues();
    if (!searchText.trim()) {
      setFilteredIssues(issues);
    } else {
      const lowerSearch = searchText.toLowerCase();
      const results = issues?.filter(issue => {
        const parent = issue.categoryStructure?.parentCategory?.title || '';
        const note = issue.note || '';
        return (
          parent.toLowerCase().includes(lowerSearch) ||
          note.toLowerCase().includes(lowerSearch)
        );
      });
      setFilteredIssues(results);
    }
  }, [searchText, pendingIssues, progressIssues, completedIssues, activeTab]);

  const tabs = ['Pending', 'In Progress', 'Completed'];

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={Colors.background} />
      <CustomHeader
        onPressNotification={() => navigation.navigate('Notification')}
        onPressProfile={() => navigation.navigate('Profile')}
      />
      <View style={styles.innerContainer}>
        {/* SEARCH BAR */}
        <Searchbar
          placeholder={'Search Issue'}
          value={searchText}
          onChangeText={text => setSearchText(text)}
          RightIcon={images.searchicon}
          containerStyle={styles.searchContainer}
        />
        {/* TOP TABS */}
        <AnimatedTabs
          tabs={tabs}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          counters={{
            Pending: pendingIssues.length,
            In_Progress: progressIssues.length,
            Completed: completedIssues.length,
          }}
        />

        {/* CLIENT ISSUES */}
        {issueLoading ? (
          <ActivityIndicator
            size={'large'}
            style={{marginTop: 40}}
            color={Colors.buttonbgcolor}
          />
        ) : activeTab === 'Pending' ? (
          <AllIssues
            issues={filteredIssues ?? pendingIssues}
            navigation={navigation}
          />
        ) : activeTab === 'In Progress' ? (
          <PendingIssues
            issues={filteredIssues ?? progressIssues}
            navigation={navigation}
          />
        ) : (
          <CompletedIssues
            issues={filteredIssues ?? completedIssues}
            navigation={navigation}
          />
        )}
      </View>
    </View>
  );
};

export default ClientDashboard;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: Platform.OS === 'android' ? 16 : 76,
  },
  innerContainer: {
    paddingHorizontal: sizes.paddingHorizontal,
    flex: 1,
  },
  searchContainer: {
    marginTop: 24,
    borderRadius: sizes.borderRadius,
  },
});
