import React, {useContext, useState} from 'react';
import {Text, View, Image, KeyboardAvoidingView, Platform} from 'react-native';
import {images} from '../../../assets/images';
import Listitems from '../../../component/Listitems/Listitems';
import {Button} from '../../../component/Button/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {BottomTabParamlist} from '../../../navigation/BottomTabs';
import {AuthContext} from '../../../../App';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Modal from 'react-native-modal';
import {styles} from './styles';
import Input from '../../../component/Input/Input';
import {firebase} from '@react-native-firebase/firestore';
import firestore from '@react-native-firebase/firestore';
import auth from '@react-native-firebase/auth';
import Spinner from 'react-native-loading-spinner-overlay';

type Props = NativeStackScreenProps<
  BottomTabParamlist & HomeStackParamsList,
  'Profile'
> & {};

const Profile: React.FC<Props> = ({navigation}) => {
  const {setUserId, userData} = useContext(AuthContext);
  const [isModalVisible, setModalVisible] = useState(false);
  const [passwordModal, setpasswordModal] = useState(false);
  const [passwordVisibility, setPasswordVisibility] = useState(false);

  const [password, setPassword] = useState('');

  const [modalContext, setModalContext] = useState<'logout' | 'deleteAccount'>(
    'logout',
  );
  const [spinner, setSpinner] = useState(false);

  const onLogoutPress = async () => {
    setUserId('');
    await AsyncStorage.clear();
  };

  const handleButtonPress = (context: 'logout' | 'deleteAccount') => {
    setModalContext(context);
    setModalVisible(true);
  };

  const handleDeleteAccount = async () => {
    setSpinner(true);
    setModalVisible(false);
    setpasswordModal(false);
    try {
      const user = auth().currentUser;

      if (user) {
        const credential = firebase.auth.EmailAuthProvider.credential(
          user.email,
          password,
        );

        await user.reauthenticateWithCredential(credential);

        await firestore().collection('Users').doc(user.uid).delete();

        await user.delete();

        await AsyncStorage.clear();
        setUserId('');
      }
    } catch (error) {
      console.error('Error deleting account:', error);
    } finally {
      setSpinner(false);
    }
  };

  return (
    <View style={styles.container}>
      <Spinner
        visible={spinner}
        textContent={'Loading...'}
        color="#000"
        textStyle={styles.spinnerTextStyle}
      />
      <Image
        style={styles.profileContainer}
        source={
          userData.profileImage?.url
            ? {uri: userData.profileImage.url}
            : images.avatarPlaceholder
        }
      />
      <Text style={styles.nameStyle}>{userData.name}</Text>
      <Text style={styles.emailStyle}>{userData.email}</Text>

      <Listitems
        leftIcon={images.usericonP}
        title="Edit Profile"
        RightIcon={images.editiconP}
        containerStyle={{marginTop: 50}}
        onPress={() => navigation.navigate('EditProfile')}
      />

      <Listitems
        leftIcon={images.lockicon}
        title="Change Password"
        RightIcon={images.editiconP}
        onPress={() => navigation.navigate('ChangePassword')}
      />

      {/* {userData.userType === 'user' ? null : (
        <Listitems
          leftIcon={images.taskiconP}
          title="My Tasks"
          RightIcon={images.righticonP}
          onPress={() => navigation.navigate('ProgressDetails', {userData})}
        />
      )} */}

      <Listitems
        leftIcon={images.securityP}
        title="Privacy"
        RightIcon={images.righticonP}
        onPress={() => navigation.navigate('Policy')}
      />

      <Listitems
        leftIcon={images.deleteicon}
        title="Delete Account"
        textContainer={{color: 'red'}}
        RightIcon={images.righticonP}
        onPress={() => handleButtonPress('deleteAccount')}
      />

      <View style={{marginTop: 35}}>
        <Button title="Log Out" onPress={() => handleButtonPress('logout')} />
      </View>

      <Modal
        isVisible={isModalVisible}
        style={{margin: 0, justifyContent: 'flex-end'}}
        animationIn={'slideInUp'}
        animationOut={'slideOutDown'}
        animationOutTiming={1500}
        onBackdropPress={() => setModalVisible(false)}>
        <View style={styles.modalContainer}>
          <Text style={styles.modalHeader}>
            {modalContext === 'logout' ? 'Log Out' : 'Delete Account'}
          </Text>
          <View style={styles.divider} />
          <Text style={styles.modalSubHeader}>
            {modalContext === 'logout'
              ? 'Are you sure you want to log out?'
              : 'Do you want to delete your account?'}
          </Text>
          <Text style={styles.modalQuestion}>
            {modalContext === 'logout' ? 'Are you Sure?' : 'Are you Sure?'}
          </Text>

          <Button
            title={
              modalContext === 'logout' ? 'Yes, Log Out' : 'Delete Account'
            }
            containerStyle={styles.logoutButton}
            onPress={() => {
              if (modalContext === 'logout') {
                onLogoutPress();
              } else {
                setModalVisible(false);

                setTimeout(() => {
                  setpasswordModal(true);
                }, 2000);
              }
            }}
          />
          <Button
            title="Cancel"
            containerStyle={styles.cancelButton}
            textContainer={styles.cancelText}
            onPress={() => setModalVisible(false)}
          />
        </View>
      </Modal>

      <Modal
        isVisible={passwordModal}
        style={{margin: 0, justifyContent: 'flex-end'}}
        animationIn={'slideInUp'}
        animationOut={'slideOutDown'}
        animationOutTiming={1500}
        onBackdropPress={() => {
          setpasswordModal(false);
          setPassword('');
        }}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'android' ? 'height' : 'padding'}>
          <View style={styles.modalContainer}>
            <Text style={styles.modalHeader}>Enter your Password</Text>

            <Input
              placeholder={'Enter your Password'}
              leftIcon={images.lockicon}
              isPassword={true}
              onRightIconPress={() =>
                setPasswordVisibility(!passwordVisibility)
              }
              secureTextEntry={passwordVisibility}
              containerStyle={{marginTop: 24}}
              onChangeText={txt => setPassword(txt)}
            />

            <Button
              title={'Continue'}
              containerStyle={styles.logoutButton}
              onPress={handleDeleteAccount}
              disabled={password.length ? false : true}
            />
            <Button
              title="Cancel"
              containerStyle={styles.cancelButton}
              textContainer={styles.cancelText}
              onPress={() => {
                setpasswordModal(false);
                setPassword('');
              }}
            />
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
};

export default Profile;
