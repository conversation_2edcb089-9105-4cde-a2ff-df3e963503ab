import {StyleSheet, Text, View} from 'react-native';
import React, {useContext, useEffect, useState} from 'react';
import ImagePicker from 'react-native-image-crop-picker';
import {appStyles, Colors, Fonts, sizes} from '../../../utilities/theme/theme';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import ProgressBar from '../../../component/common/ProgressBar/ProgressBar';
import AttachmentComp from '../../../component/common/AttachmentComp/AttachmentComp';
import SelectorItem from '../../../component/common/SelectorItem/SelectorItem';
import {
  CalendarIcon2,
  MemberIcon,
  NotificationStatus,
  PriorityIcon,
} from '../../../assets/svgIcons';
import MultiLineNoteInput from '../../../component/common/MultiLineNoteInput';
import {Button} from '../../../component/Button/Button';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';
import FormInput from '../../../component/FormInput/FormInput';
import OptionsModal from '../../../component/modals/OptionsModal';
import {uploadMultipleAttachment} from '../../../helpers/uploadMultipleAttachment';
import {AuthContext} from '../../../../App';
import {getFutureTimestampFromDuration} from '../../../helpers/getFutureTimestampFromDuration';
import {firebase} from '@react-native-firebase/firestore';
import {IUser} from '../../../interfaces/IUser';
import {showToast} from '../../../helpers/toast';
import ChooseIssueModal from '../../../component/modals/ChooseIssueModal';

type Props = NativeStackScreenProps<HomeStackParamsList, 'CreateTask'>;
const MAX_ATTACHMENTS = 3;

const validationSchema = Yup.object().shape({
  title: Yup.string().required('Title is required'),
  notes: Yup.string().required('Notes is required'),
  attachments: Yup.array<Attachment>().max(
    MAX_ATTACHMENTS,
    `Max ${MAX_ATTACHMENTS} attachments`,
  ), // Optional, not required
  priority: Yup.string().required('Priority is required'),
  member: Yup.object({
    value: Yup.string().required('Value is required'),
    id: Yup.string().required('ID is required'),
  }).required('Member is required'),
  duration: Yup.string().required('Duration is required'),
});
interface Attachment {
  uri: string;
  type: string;
  name: string;
}

interface Option {
  value: string;
  id: string;
}

interface FormValues {
  title: string;
  notes: string;
  attachments?: Attachment[];
  priority: string;
  member: Option;
  duration: string;
  issueId?: string;
}

const CreateTask: React.FC<Props> = ({navigation, route}) => {
  const {issueId, isFromIssueTaskScreen} = route?.params || {};

  const {userId} = useContext(AuthContext);
  const insets = useSafeAreaInsets();
  const [issueModal, setIssueModal] = useState(false);
  const [priorityModal, setPriorityModal] = useState(false);
  const [assignToModal, setAssignToModal] = useState(false);
  const [durationModalModal, setDurationModal] = useState(false);
  const [staffUsers, setStaffUsers] = useState<IUser[]>([]);
  const [staffLoading, setStaffLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedUserName, setSelectedUserName] = useState<string>('');

  const formik = useFormik<FormValues>({
    initialValues: {
      title: '',
      notes: '',
      attachments: [] as Attachment[],
      priority: '',
      member: {} as Option,
      duration: '',
      issueId: issueId || '',
    },
    validationSchema: validationSchema,
    onSubmit: async values => handleCreateTask(values),
  });

  const handleCreateTask = async (values: FormValues) => {
    try {
      setIsSubmitting(true);

      // Step 1: Fetch issue data by ID
      const issueDoc = await firebase
        .firestore()
        .collection('Issues')
        .doc(issueId)
        .get();

      if (!issueDoc.exists) {
        showToast('Issue not found!', 'Error', 'error');
        return;
      }
      const issueData = issueDoc.data();
      const existingAssignedStaff: string[] = issueData?.assignedStaff || [];

      const staffId = values.member.id;
      const isAlreadyAssigned = existingAssignedStaff.includes(staffId);

      // Add current staffId if not already in the list
      const updatedAssignedStaff = isAlreadyAssigned
        ? existingAssignedStaff
        : [...existingAssignedStaff, staffId];

      // Step 2: Upload attachments if any
      const uploadedAttachments = values.attachments?.length
        ? await uploadMultipleAttachment(values.attachments, userId)
        : [];

      // Step 3: Prepare payload
      const payload = {
        createdAt: firebase.firestore.FieldValue.serverTimestamp(),
        updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
        issueId: values.issueId,
        attachments: uploadedAttachments,
        managerId: userId,
        title: values.title,
        note: values.notes,
        priority: values.priority,
        staffId: values.member.id,
        duration: values.duration,
        durationTimeStamp: getFutureTimestampFromDuration(values.duration),
        status: 'progress',
        assignedStaff: updatedAssignedStaff,
      };

      // Step 4: Save task
      await firebase.firestore().collection('Tasks').add(payload);
      await firebase.firestore().collection('Issues').doc(issueId).update({
        assignedStaff: updatedAssignedStaff,
      });

      showToast('Task created successfully!', 'Success', 'success');
      navigation.goBack();
      formik.resetForm();
      setSelectedUserName('');
    } catch (error) {
      console.error('Error creating task:', error);
      showToast('Failed to create task', 'Error', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStaffUsers = async () => {
    setStaffLoading(true);
    try {
      const snapShot = await firebase
        .firestore()
        .collection('Users')
        .where('managerId', '==', userId)
        .get();

      const users: IUser[] = snapShot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as Omit<IUser, 'id'>),
      }));
      setStaffUsers(users);
    } catch (error) {
      console.log('errror-------', error);
    } finally {
      setStaffLoading(false);
    }
  };

  const handleAttachmentPick = async (index?: number) => {
    try {
      const image = await ImagePicker.openPicker({
        cropping: true,
        compressImageQuality: 0.8,
        mediaType: 'photo',
      });

      const newAttachment: Attachment = {
        uri: image.path,
        type: image.mime,
        name: `image-${Date.now()}.jpg`,
      };

      const current = [...formik.values.attachments];

      if (typeof index === 'number') {
        current[index] = newAttachment; // Replace at index
      } else {
        if (current.length >= MAX_ATTACHMENTS) return;
        current.push(newAttachment);
      }

      formik.setFieldValue('attachments', current);
    } catch (error) {
      console.warn('Image picker error:', error);
    }
  };

  const handleRemoveAttachment = (index: number) => {
    const updated = [...(formik?.values?.attachments ?? [])];
    updated.splice(index, 1);
    formik.setFieldValue('attachments', updated);
  };

  useEffect(() => {
    getStaffUsers();
  }, []);

  const isFormValid = Object.values(formik.values).every(value => value !== '');
  const {priority, duration, member} = formik.values;

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}>
        <ProgressBar max={2} value={1} showBottomText />
        <View style={styles.innerContainer}>
          <Text style={appStyles.h5}>Attachment</Text>
          <Text style={styles.attachmentNote}>
            Format should be in .jpeg .png less than 5MB
          </Text>
          <View style={styles.attachmentsContainer}>
            {[0, 1, 2].map(i => {
              const item = formik?.values?.attachments?.[i];
              return (
                <AttachmentComp
                  key={i}
                  url={item?.uri}
                  onPress={() =>
                    handleAttachmentPick(
                      formik?.values?.attachments &&
                        i < formik.values.attachments.length
                        ? i
                        : undefined,
                    )
                  }
                  onRemove={item ? () => handleRemoveAttachment(i) : undefined}
                />
              );
            })}
          </View>

          {/* Onboarding Screen Selector */}
          <View style={styles.selectorContainer}>
            <FormInput
              label="Task Title"
              placeholder="Enter Task Title"
              placeholderTextColor="#98A2B3"
              icon={<NotificationStatus />}
              value={formik.values.title}
              onChangeText={formik.handleChange('title')}
            />
            <MultiLineNoteInput
              labelStyles={styles.multiLineLabel}
              style={styles.multiLineInput}
              placeholder="Enter Task Description"
              placeholderTextColor="#98A2B3"
              value={formik.values.notes}
              onChangeText={text => formik.setFieldValue('notes', text)}
            />
            {isFromIssueTaskScreen ? null : (
              <SelectorItem
                title={selectedUserName ? selectedUserName : 'Choose Issue'}
                icon={<NotificationStatus />}
                onPress={() => setIssueModal(true)}
                label="Issue"
                color={selectedUserName ? '#475467' : '#98A2B3'}
              />
            )}
            <SelectorItem
              title={priority ? priority : 'Select Priority'}
              icon={<PriorityIcon />}
              onPress={() => setPriorityModal(true)}
              label="Priority"
              color={priority ? '#475467' : '#98A2B3'}
            />
            <SelectorItem
              title={member.value ? member.value : 'Assign To'}
              icon={<MemberIcon />}
              onPress={() => setAssignToModal(true)}
              label="Assign To"
              color={member.id ? '#475467' : '#98A2B3'}
            />
            <SelectorItem
              title={duration ? duration : 'Select Duration'}
              icon={<CalendarIcon2 />}
              onPress={() => setDurationModal(true)}
              label="Select Duration"
              color={duration ? '#475467' : '#98A2B3'}
            />
          </View>
        </View>
      </KeyboardAwareScrollView>
      {/* Submit Button */}
      <View style={appStyles.bottomButtonContainer}>
        <Button
          title="Create Task"
          containerStyle={{
            marginBottom: isFromIssueTaskScreen ? insets.bottom : 8,
          }}
          onPress={formik.handleSubmit}
          disabled={!isFormValid || isSubmitting}
          isLoading={isSubmitting}
        />
      </View>

      <OptionsModal
        visible={priorityModal}
        title="Priority"
        description="Select the priority"
        options={[
          {value: 'Low', id: 'Low'},
          {value: 'Medium', id: 'Medium'},
          {value: 'High', id: 'High'},
        ]}
        selectedValue={formik.values.priority}
        onSelect={(data: Option) =>
          formik.setFieldValue('priority', data.value)
        }
        onCancel={() => setPriorityModal(false)}
        onRequest={() => setPriorityModal(false)}
        isRequestDisabled={formik.values.priority ? true : false}
      />
      <OptionsModal
        visible={assignToModal}
        title="Assign To"
        description="Who will finish this task"
        options={staffUsers.map(user => ({value: user.name, id: user.id}))}
        selectedValue={formik.values.member.value}
        onSelect={(value: Option) => formik.setFieldValue('member', value)}
        onCancel={() => setAssignToModal(false)}
        onRequest={() => setAssignToModal(false)}
        isRequestDisabled={formik.values.member.id ? true : false}
        loading={staffLoading}
      />
      <OptionsModal
        visible={durationModalModal}
        title="Select Duration"
        description="Select the hours for this task"
        options={[
          {value: '24 hours', id: '24'},
          {value: '48 hours', id: '48'},
          {value: '72 hours', id: '72'},
          {value: '96 hours', id: '96'},
        ]}
        selectedValue={formik.values.duration}
        onSelect={(data: Option) =>
          formik.setFieldValue('duration', data.value)
        }
        onCancel={() => setDurationModal(false)}
        onRequest={() => setDurationModal(false)}
        isRequestDisabled={formik.values.duration ? true : false}
      />
      <ChooseIssueModal
        visible={issueModal}
        selectedValue={formik.values.issueId || ''}
        onSelect={(id: string, parentCatName: string) => {
          setSelectedUserName(parentCatName);
          formik.setFieldValue('issueId', id);
        }}
        onCancel={() => {
          setIssueModal(false);
          formik.setFieldValue('issueId', '');
        }}
        onRequest={() => setIssueModal(false)}
        isRequestDisabled={formik.values.issueId ? false : true}
      />
    </View>
  );
};

export default CreateTask;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: 16,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: sizes.paddingHorizontal,
  },
  innerContainer: {
    backgroundColor: '#FEFEFE',
    paddingVertical: 24,
    paddingHorizontal: 16,
    borderRadius: 16,
    marginTop: 20,
    flex: 1,
    marginBottom: 24,
  },
  attachmentNote: {
    paddingTop: 1,
  },
  attachmentsContainer: {
    flexDirection: 'row',
    marginTop: 16,
    justifyContent: 'space-between',
  },
  selectorContainer: {
    gap: 12,
    marginTop: 16,
  },
  multiLineLabel: {
    fontSize: 10,
    color: '#475467',
    fontFamily: Fonts.Regular,
    marginBottom: 6,
  },
  multiLineInput: {
    borderWidth: 1,
    borderColor: '#98A2B3',
  },
});
