import {
  ActivityIndicator,
  StyleSheet,
  Text,
  View,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import React, {useEffect, useState, useContext} from 'react';
import {appStyles, Colors, sizes} from '../../../utilities/theme/theme';
import {Fonts} from '../../../../srcOld/utilities/theme/theme';
import AppSlider from '../../../component/common/AppSlider/AppSlider';
import {getColorByStatus} from '../../../helpers/getColorByStatus';
import {CalendarStroke, EditOutline} from '../../../assets/svgIcons';
import HorizontalLine from '../../../component/common/HorizontalLine/HorizontalLine';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import moment from 'moment';
import {getHoursLeft} from '../../../helpers/getHoursLeft';
import UserCard from '../../../component/common/UserCard/UserCard';
import {getUserById} from '../../../helpers/getUserById';
import CommentSection from '../../../component/CommentSection/CommentSection';
import CommentBox from '../../../component/CommentSection/CommentBox';
import {AuthContext} from '../../../../App';
import firestore from '@react-native-firebase/firestore';
import {addCommentToTask} from '../../../backend/comments';
import {images} from '../../../assets/images';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
type Props = NativeStackScreenProps<HomeStackParamsList, 'TaskDetails'>;

const TaskDetails: React.FC<Props> = ({navigation, route}) => {
  const {taskDetails, staffUser} = route.params ?? {};
  const [loading, setLoading] = useState(false);
  const {userData, userId} = useContext(AuthContext);
  const insets = useSafeAreaInsets();

  useEffect(() => {
    const fetchUser = async () => {
      if (!staffUser && taskDetails?.staffId) {
        setLoading(true);
        try {
          const userData = await getUserById(taskDetails.staffId);
          if (userData) {
            navigation.setParams({staffUser: userData});
          }
        } catch (error) {
          console.error('Failed to fetch user:', error);
        }
      }
      setLoading(false);
    };

    fetchUser();
  }, [taskDetails?.staffId]);

  const handleSendComment = async (message: string) => {
    try {
      // Create new comment (without id since Firestore will generate it)
      const newComment = {
        message,
        senderId: userId,
        createdAt: firestore.Timestamp.now(),
      };

      // Use helper function to add comment
      const success = await addCommentToTask(taskDetails.id, newComment);

      if (!success) {
        console.error('Failed to add comment');
      }
    } catch (error) {
      console.error('Error sending comment:', error);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}>
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        <View style={styles.innerContainer}>
          {/* TITLE & DATE */}
          <View style={[appStyles.flexSpaceBetween]}>
            <Text style={appStyles.h4}>{taskDetails.title}</Text>
            {/* STATUS */}
            <View
              style={[
                styles.statusContainer,
                {backgroundColor: getColorByStatus('progress'), marginTop: 2},
              ]}>
              <Text style={styles.statusText}>{taskDetails.status}</Text>
            </View>
          </View>
          <Text style={styles.createdDate}>
            Created on{' '}
            {moment(taskDetails.createdAt.toDate()).format('ddd DD MMM YYYY')}
          </Text>
          {/* APP INTRO SLIDER */}
          {taskDetails.attachments.length ? (
            <AppSlider images={taskDetails.attachments.map(img => img.url)} />
          ) : (
            <Text style={styles.emptyText}>No attachment found</Text>
          )}

          {/* PRIORITY */}
          <View style={styles.row}>
            <Text style={styles.label}>Priority</Text>
            <View
              style={[styles.statusContainer, {backgroundColor: '#19211E'}]}>
              <Text style={styles.statusText}>{taskDetails.priority}</Text>
            </View>
          </View>
          {/* DESCRIPTION CANTAINER */}
          <View style={styles.descriptionContainer}>
            <Text style={styles.title}>Description</Text>
            <Text style={styles.description}>{taskDetails.note}</Text>
          </View>
          {/* ASSIGN AND AVATAR */}
          <View style={[appStyles.flexSpaceBetween, styles.assigneeHeader]}>
            <Text style={styles.sectionTitle}>Assignee</Text>
            <View style={styles.reassignContainer}>
              <Text style={styles.statusText}>Request To Re Assign</Text>
              <EditOutline />
            </View>
          </View>

          {loading ? (
            <ActivityIndicator
              color={Colors.buttonbgcolor}
              style={{marginTop: 16}}
            />
          ) : (
            <UserCard
              imageUrl={staffUser?.profileImage?.url || ''}
              name={staffUser?.name || ''}
              email={staffUser?.email}
              date={taskDetails.durationTimeStamp}
            />
          )}
          {/* <View style={styles.avatarRow}>
            {Array.from({length: 3}).map((_, index) => (
              <Image
                key={index}
                style={styles.avatar}
                source={{uri: 'https://randomuser.me/api/portraits/men/1.jpg'}}
              />
            ))}
          </View> */}
          {/* PROGRESS BAR */}
          {/* <View style={[appStyles.flexSpaceBetween, styles.progressSection]}>
            <ProgressBar
              max={100}
              value={80}
              containerStyle={styles.progressBarContainer}
              filledStyle={styles.filledStyle}
              unfilledStyle={styles.unfilledStyle}
              showBottomText={false}
            />
            <Text style={styles.percentageText}>80%</Text>
          </View> */}
          {/* DEAD LINES */}
          <View style={[appStyles.flexSpaceBetween, styles.deadlineRow]}>
            <View style={styles.deadlineLeft}>
              <CalendarStroke />
              <Text style={styles.sectionTitle}>
                Deadline:{' '}
                {moment(taskDetails.durationTimeStamp.toDate()).format(
                  'MMMM D.YYYY',
                )}
              </Text>
            </View>
            <View style={styles.deadlineRight}>
              <Text style={styles.statusText}>
                {getHoursLeft(taskDetails.durationTimeStamp).toString() +
                  ' hours Left'}
              </Text>
              <EditOutline />
            </View>
          </View>
          {/* ACTIVITY */}
          <HorizontalLine marginVertical={16} />
        </View>
        <CommentSection taskDetails={taskDetails} />
      </ScrollView>

      {/* Sticky Comment Input Box - Positioned Absolutely */}
      <View
        style={[styles.stickyCommentBox, {paddingBottom: 12 + insets.bottom}]}>
        <CommentBox
          onSendComment={handleSendComment}
          currentUserImage={
            userData?.profileImage?.url || String(images.avatarPlaceholder)
          }
        />
      </View>
    </KeyboardAvoidingView>
  );
};

export default TaskDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  keyboardContainer: {
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 16,
  },
  scrollContent: {
    paddingBottom: 100, // Add padding to account for sticky comment box
  },
  innerContainer: {
    backgroundColor: Colors.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingHorizontal: 16,
    paddingTop: 24,
  },
  stickyCommentBox: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.white,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#EAECF0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  createdDate: {
    fontSize: 12,
    color: '#475467',
    fontFamily: Fonts.Medium,
  },
  row: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 16,
    alignItems: 'center',
  },
  label: {
    fontSize: 12,
    color: '#080C10',
    fontFamily: Fonts.Semibold,
  },
  statusContainer: {
    alignSelf: 'flex-start',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
  },
  statusText: {
    color: 'white',
    fontFamily: Fonts.Regular,
    fontSize: 8,
    textTransform: 'capitalize',
  },
  descriptionContainer: {
    borderWidth: 1,
    borderColor: '#EAECF0',
    marginTop: 16,
    padding: 12,
    borderRadius: 12,
  },
  title: {
    fontSize: 12,
    color: Colors.primary_text,
    fontFamily: Fonts.Semibold,
  },
  description: {
    fontSize: 10,
    color: '#475467',
    paddingTop: 4,
    fontFamily: Fonts.Medium,
  },
  sectionTitle: {
    fontSize: 12,
    fontFamily: Fonts.Semibold,
    color: '#080C10',
  },
  assigneeHeader: {
    marginTop: 16,
  },
  reassignContainer: {
    backgroundColor: Colors.buttonbgcolor,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
  },
  avatarRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginTop: 12,
  },
  avatar: {
    width: 42,
    height: 42,
    borderRadius: 21,
    borderWidth: 1.6,
    borderColor: '#898989',
  },
  progressSection: {
    marginTop: 16,
  },
  progressBarContainer: {
    marginTop: 0,
    width: '88%',
  },
  filledStyle: {
    backgroundColor: '#48D25E',
    height: 5,
    borderRadius: 10,
  },
  unfilledStyle: {
    backgroundColor: '#F5F5F5',
    height: 5,
    borderRadius: 10,
  },
  percentageText: {
    fontSize: 10,
    color: '#4B5563',
    fontFamily: Fonts.Semibold,
  },
  deadlineRow: {
    marginTop: 14,
  },
  deadlineLeft: {
    flexDirection: 'row',
    gap: 12,
    alignItems: 'center',
  },
  deadlineRight: {
    backgroundColor: '#B62023',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
  },
  emptyText: {
    textAlign: 'center',
    fontFamily: Fonts.Semibold,
    color: Colors.placeholder,
    marginVertical: 24,
    marginTop: 32,
    fontSize: 12,
  },
});
