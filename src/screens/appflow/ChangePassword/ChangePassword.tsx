import React, {useState} from 'react';
import {Text, StyleSheet, View, ScrollView, Alert} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import Input from '../../../component/Input/Input';
import {images} from '../../../assets/images';
import {Button} from '../../../component/Button/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import auth from '@react-native-firebase/auth';

type Props = NativeStackScreenProps<HomeStackParamsList, 'ChangePassword'>;

const ChangePassword: React.FC<Props> = ({navigation}) => {
  const [hidePassword, setHidePassword] = useState(true);
  const [confirmHidePassword, setConfirmHidePassword] = useState(true);
  const [oldPassword, setOldPassword] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const togglePassword = () => setHidePassword(!hidePassword);
  const toggleConfirmPassword = () =>
    setConfirmHidePassword(!confirmHidePassword);
  const toggleOldPassword = () => setOldPassword(!oldPassword);

  const validationSchema = Yup.object().shape({
    oldPassword: Yup.string().required('Old Password is required'),
    password: Yup.string()
      .required('Password is required')
      .min(8, 'Password must be at least 8 characters'),
    confirmPassword: Yup.string()
      .required('Confirm Password is required')
      .oneOf([Yup.ref('password')], 'Passwords must match'),
  });

  const formik = useFormik({
    initialValues: {
      oldPassword: '',
      password: '',
      confirmPassword: '',
    },
    validationSchema: validationSchema,
    onSubmit: values => {
      changePassword(values);
    },
  });

  const changePassword = async (values: {
    oldPassword: string;
    password: string;
  }) => {
    const user = auth().currentUser;
    if (user && user.email) {
      const email = user.email;
      const credential = auth.EmailAuthProvider.credential(
        email,
        values.oldPassword,
      );

      try {
        setIsLoading(true);
        await user.reauthenticateWithCredential(credential);
        await user.updatePassword(values.password);
        navigation.navigate('UpdatedPassword');
      } catch (error) {
        throw new Error('Failed to update password: ');
      } finally {
      }
    } else {
      throw new Error('No user is signed in');
    }
    setIsLoading(false);
  };

  return (
    <ScrollView>
      <View style={styles.container}>
        <Text style={styles.mainTitle}>Create New Password</Text>
        <Text style={styles.subTitle}>
          Change your password to something safer and easier to remember.
        </Text>

        <Input
          title="Old Password"
          placeholder="Enter Old Password"
          leftIcon={images.lockicon}
          isPassword={true}
          secureTextEntry={oldPassword}
          onRightIconPress={toggleOldPassword}
          containerStyle={{marginTop: 12}}
          onChangeText={formik.handleChange('oldPassword')}
          value={formik.values.oldPassword}
          onBlur={formik.handleBlur('oldPassword')}
          onLeftIconPress={toggleOldPassword}
        />
        {formik.touched.oldPassword && formik.errors.oldPassword ? (
          <Text style={styles.errorText}>{formik.errors.oldPassword}</Text>
        ) : null}

        <Input
          title="New Password"
          placeholder="Enter New Password"
          leftIcon={images.lockicon}
          isPassword={true}
          secureTextEntry={hidePassword}
          onRightIconPress={togglePassword}
          containerStyle={{marginTop: 16}}
          onChangeText={formik.handleChange('password')}
          value={formik.values.password}
          onBlur={formik.handleBlur('password')}
          onLeftIconPress={togglePassword}
          onFocus={formik.handleBlur('password')}
        />
        {formik.touched.password && formik.errors.password ? (
          <Text style={styles.errorText}>{formik.errors.password}</Text>
        ) : null}

        <Input
          title="Confirm Password"
          placeholder="Confirm Password"
          leftIcon={images.lockicon}
          isPassword={true}
          secureTextEntry={confirmHidePassword}
          onRightIconPress={toggleConfirmPassword}
          containerStyle={{marginTop: 16}}
          onChangeText={formik.handleChange('confirmPassword')}
          value={formik.values.confirmPassword}
          onBlur={formik.handleBlur('confirmPassword')}
          onLeftIconPress={toggleConfirmPassword}
          onFocus={formik.handleBlur('password')}
        />
        {formik.touched.confirmPassword && formik.errors.confirmPassword ? (
          <Text style={styles.errorText}>{formik.errors.confirmPassword}</Text>
        ) : null}

        <View style={{marginTop: 59}}>
          <Button
            title="Update"
            onPress={formik.handleSubmit}
            isLoading={isLoading}
            disabled={isLoading}
          />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: Colors.background,
  },
  mainTitle: {
    fontSize: 20,
    fontFamily: Fonts.Bold,
    color: Colors.primary,
    paddingTop: 22,
  },
  subTitle: {
    fontSize: 14,
    fontFamily: Fonts.Medium,
    color: Colors.lightcolor,
    lineHeight: 21,
    paddingTop: 12,
  },
  errorText: {
    color: Colors.red,
    fontSize: 12,
    marginTop: 5,
  },
});

export default ChangePassword;
