import {StyleSheet} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {ms, vs} from 'react-native-size-matters';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: 22,
  },
  textContainer: {
    flexDirection: 'column',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 22,
  },
  titleStyle: {
    fontSize: 14,
    fontFamily: Fonts.Semibold,
    color: Colors.lightcolor,
  },
  subTitle: {color: 'black', fontSize: 18, fontFamily: Fonts.Bold},
  headlogo: {
    width: 47,
    height: 47,
    borderRadius: 100,
    borderWidth: 1,
    borderColor: Colors.buttonbgcolor,
  },
  notificationicon: {
    width: 24,
    height: 24,
    marginRight: 14,
    alignSelf: 'center',
  },
  taskText: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 30,
    paddingHorizontal: 22,
  },
  completeTask: {
    fontSize: 12,
    color: Colors.primary,
    fontFamily: Fonts.Semibold,
  },
  seeallText: {
    fontSize: 12,
    fontFamily: Fonts.Medium,
    color: Colors.lightcolor,
  },
  taskContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  emptyText: {
    textAlign: 'center',
    fontFamily: Fonts.Semibold,
    color: Colors.placeholder,
    marginTop: vs(10),
    fontSize: ms(12),
  },
  completedTaskContainer: {
    paddingHorizontal: 20,
    gap: 10,
    marginTop: 12,
  },
  emptyDesc: {
    fontFamily: Fonts.Medium,
    fontSize: 14,
    color: Colors.placeholder,
    marginTop: 4,
  },
  emptyTitle: {
    fontFamily: Fonts.Bold,
    fontSize: 16,
    color: Colors.placeholder,
    marginTop: 30,
  },
  innerEmptyContainer: {alignItems: 'center', marginTop: 76, marginBottom: 64},
  searchContainer: {marginTop: 8, marginHorizontal: 22, paddingBottom: 10},
  rightActionContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: ms(60),
    backgroundColor: '#55AB67',
    marginTop: 12,
    marginLeft: -20,
  },
});
