import React, {useContext, useEffect, useState} from 'react';
import {
  Text,
  View,
  Image,
  TouchableOpacity,
  FlatList,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import {images} from '../../../assets/images';
import Searchbar from '../../../component/Searchbar/Searchbar';
import Task from '../../../component/Task/Task';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import OngoingTaskList from '../../../component/OngoingTaskList/Ongoingtasklist';
import {AuthContext} from '../../../../App';
import {ITask} from '../../../interfaces/ITask';
import moment from 'moment';
import {styles} from './styles';
import {
  HomeEmptyState,
  MarkDone,
  OngoingEmptyState,
} from '../../../assets/svgIcons';
import {Button} from '../../../component/Button/Button';
import {useDebounce} from 'use-debounce';
import {GestureHandlerRootView, Swipeable} from 'react-native-gesture-handler';
import {Colors} from '../../../utilities/theme/theme';
import useTasks from '../../../hooks/useTasks';

type Props = NativeStackScreenProps<HomeStackParamsList, 'Home'>;

const Home: React.FC<Props> = ({navigation}) => {
  const [searchText, setSearchText] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const {fetchTasks, tasks, isLoading, markTaskAsDone} = useTasks();
  const {fetchTasks: fetchOngoing, tasks: ongoing} = useTasks();
  const {fetchTasks: fetchCompleted, tasks: completed} = useTasks();

  const [value] = useDebounce(searchText, 1000);
  const {userData, userId} = useContext(AuthContext);

  useEffect(() => {
    fetchTasks();
    fetchOngoing('pending', 'date', 10);
    fetchCompleted('done', 'date', 4);
  }, [userId]);

  const renderSwipeableTask = (item: ITask) => (
    <Swipeable
      renderRightActions={() => (
        <TouchableOpacity
          style={styles.rightActionContainer}
          onPress={() => markTaskAsDone(item.id)}>
          <MarkDone />
        </TouchableOpacity>
      )}>
      <OngoingTaskList
        title={item.title}
        isCompleted={item.status == 'done' ? true : false}
        image={item.users}
        date={moment(item.date.toDate()).format('DD MMM')}
        incentive={item.incentive ? item.incentive : 0}
        onPress={() =>
          navigation.navigate('TaskDetails', {
            isCompleted: false,
            item: item,
            user: userData,
          })
        }
      />
    </Swipeable>
  );

  const filteredOngoingTasks = tasks?.filter(task =>
    task.title.toLowerCase().includes(value.toLowerCase()),
  );

  const searchTitle = isSearching ? 'Search Results' : 'Ongoing Tasks';
  const emptyMessage = isSearching
    ? 'No tasks found'
    : `There is no ongoing task available`;
  return (
    <GestureHandlerRootView
      style={{flex: 1, backgroundColor: Colors.background}}>
      <View style={styles.container}>
        <SafeAreaView />
        <View style={styles.headerContainer}>
          <View style={styles.textContainer}>
            <Text style={styles.titleStyle}>Welcome Back!</Text>
            <Text style={styles.subTitle}>{userData.name}</Text>
          </View>
          <View style={{flexDirection: 'row'}}>
            <TouchableOpacity
              style={{alignSelf: 'center'}}
              onPress={() => navigation.navigate('Notification')}>
              <Image
                style={styles.notificationicon}
                source={images.notificationicon}
                resizeMode="contain"
              />
            </TouchableOpacity>
            <TouchableOpacity onPress={() => navigation.navigate('Profile')}>
              <Image
                style={styles.headlogo}
                source={
                  userData.profileImage?.url
                    ? {uri: userData.profileImage.url}
                    : images.avatarPlaceholder
                }
              />
            </TouchableOpacity>
          </View>
        </View>

        <Searchbar
          placeholder={'Search Task'}
          value={searchText}
          onChangeText={i => {
            setSearchText(i);
            setTimeout(() => {
              setIsSearching(i.trim().length > 0);
            }, 1000);
          }}
          RightIcon={images.searchicon}
          containerStyle={styles.searchContainer}
        />
        {isLoading ? (
          <ActivityIndicator
            size={'large'}
            color={Colors.buttonbgcolor}
            style={{alignSelf: 'center', marginTop: 100}}
          />
        ) : tasks.length ? (
          <ScrollView showsVerticalScrollIndicator={false}>
            {isSearching ? null : !completed?.length ? null : (
              <>
                <View style={styles.taskText}>
                  <Text style={styles.completeTask}>Completed Tasks</Text>
                  <TouchableOpacity
                    onPress={() => navigation.navigate('CompleteTask')}>
                    <Text style={styles.seeallText}>See all</Text>
                  </TouchableOpacity>
                </View>
                <View>
                  <ScrollView
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.completedTaskContainer}
                    horizontal>
                    {completed?.map((item, index) => (
                      <Task
                        key={index}
                        varient={'primary'}
                        title={item.title}
                        image={item.users}
                        numText={item.incentive ? item.incentive : 0}
                        onPress={() =>
                          navigation.navigate('TaskDetails', {
                            isCompleted: false,
                            item: item,
                            user: userData,
                          })
                        }
                      />
                    ))}
                  </ScrollView>
                </View>
              </>
            )}

            <View style={styles.taskText}>
              <Text style={styles.completeTask}>{searchTitle}</Text>
              <TouchableOpacity
                onPress={() => navigation.navigate('OngoingTask')}>
                <Text style={styles.seeallText}>See all</Text>
              </TouchableOpacity>
            </View>

            <FlatList
              showsVerticalScrollIndicator={false}
              data={isSearching ? filteredOngoingTasks : ongoing}
              contentContainerStyle={{paddingBottom: 40}}
              renderItem={({item}) => {
                return renderSwipeableTask(item);
              }}
              ListEmptyComponent={() => (
                <View style={{alignItems: 'center', marginTop: 40}}>
                  <OngoingEmptyState height={130} />
                  <Text style={styles.emptyText}>{emptyMessage}</Text>
                </View>
              )}
            />
          </ScrollView>
        ) : (
          <View style={{paddingHorizontal: 24}}>
            <View style={styles.innerEmptyContainer}>
              <HomeEmptyState />
              <Text style={styles.emptyTitle}>No Tasks</Text>
              <Text style={styles.emptyDesc}>
                It seems you are not assigned any task yet
              </Text>
            </View>
            <Button
              title="Add Task"
              onPress={() => navigation.navigate('Addtask')}
            />
          </View>
        )}
      </View>
    </GestureHandlerRootView>
  );
};

export default Home;
