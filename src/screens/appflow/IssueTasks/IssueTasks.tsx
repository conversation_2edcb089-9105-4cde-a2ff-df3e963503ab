import {
  ActivityIndicator,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useContext, useEffect, useMemo, useRef, useState} from 'react';
import {appStyles, Colors, Fonts, sizes} from '../../../utilities/theme/theme';
import {HomeStackParamsList} from '../../../navigation/HomeStackNavigation';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {
  ArrowRight,
  CalendarIcon2,
  PriorityIcon,
} from '../../../assets/svgIcons';
import {firebase} from '@react-native-firebase/firestore';
import {IUser} from '../../../interfaces/IUser';
import IssueDetailsSheet, {
  IssueDetailsSheetRef,
} from '../../../component/modals/IssueDetailsSheet';
import ProgressBar from '../../../component/common/ProgressBar/ProgressBar';
import HorizontalLine from '../../../component/common/HorizontalLine/HorizontalLine';
import ManagerTaskCard from '../../../component/ManagerTaskCard/ManagerTaskCard';
import {Button} from '../../../component/Button/Button';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {AuthContext} from '../../../../App';
import firestore from '@react-native-firebase/firestore'; // or 'firebase/firestore' for web
import {ITask} from '../../../interfaces/ITask';
import {getHoursLeft} from '../../../helpers/getHoursLeft';
import UserCard from '../../../component/common/UserCard/UserCard';
import SelectorItem from '../../../component/common/SelectorItem/SelectorItem';
import OptionsModal from '../../../component/modals/OptionsModal';
import {getFutureTimestampFromDuration} from '../../../helpers/getFutureTimestampFromDuration';
import {getColorByStatus} from '../../../helpers/getColorByStatus';
import {IIssue} from '../../../interfaces/IIssue';
type Props = NativeStackScreenProps<HomeStackParamsList, 'IssueTasks'>;

interface Option {
  value: string;
  id: string;
}
const IssueTasks: React.FC<Props> = ({navigation, route}) => {
  const {id} = route.params.issue;
  const insets = useSafeAreaInsets();
  const {userId} = useContext(AuthContext);
  const [issue, setIssue] = useState<IIssue>();
  const [issueLoading, setIssueLoading] = useState(true);
  const [loadingUser, setLoadingUser] = useState(true);
  const [user, setUser] = useState<IUser>();
  const [tasks, setTasks] = useState<ITask[]>([]);
  const [loading, setLoading] = useState(true);
  const [durationModalModal, setDurationModal] = useState(false);
  const [issueDeadline, setIssueDeadline] = useState<string>('');
  const [priorityModal, setPriorityModal] = useState(false);
  const [issuePriority, setIssuePriority] = useState<string>('');
  const [issueStatusModal, setIssueStatusModal] = useState(false);
  const [issueStatus, setIssueStatus] = useState<string>('');

  const issueSheetRef = useRef<IssueDetailsSheetRef>(null);
  const openSheet = () => issueSheetRef.current?.open();

  useEffect(() => {
    const getIssueById = async () => {
      try {
        const doc = await firestore().collection('Issues').doc(id).get();
        if (doc.exists) {
          const data = doc.data();
          setIssue({id: doc.id, ...data} as IIssue);
          setIssueDeadline(data?.duration || '');
          setIssuePriority(data?.priority || '');
          setIssueStatus(data?.status || '');
        } else {
          console.warn('Issue not found');
        }
      } catch (error) {
        console.error('Error fetching issue:', error);
      } finally {
        setIssueLoading(false);
      }
    };

    getIssueById();
  }, [id]);

  useEffect(() => {
    if (!userId) return;
    const unsubscribe = firestore()
      .collection('Tasks')
      .where('issueId', '==', id)
      .onSnapshot(
        snapshot => {
          const updatedTasks = snapshot.docs.map(
            doc =>
              ({
                id: doc.id,
                ...doc.data(),
              } as ITask),
          );
          setTasks(updatedTasks);
          setLoading(false);
        },
        error => {
          console.error('Error fetching tasks:', error);
          setLoading(false);
        },
      );

    return () => unsubscribe(); // Clean up on unmount
  }, [userId]);

  useEffect(() => {
    if (!issue?.userId) return;

    const fetchUser = async () => {
      try {
        const userDoc = await firebase
          .firestore()
          .collection('Users')
          .doc(issue.userId)
          .get();
        if (userDoc.exists) {
          setUser(userDoc.data() as IUser);
        }
      } catch (error) {
        console.error('Error fetching user:', error);
      } finally {
        setLoadingUser(false);
      }
    };

    fetchUser();
  }, [id, issue]);

  const updateIssue = async (payload: Partial<ITask>) => {
    return await firestore()
      .collection('Issues')
      .doc(id)
      .update(payload)
      .then(() => {
        setIssue({...issue, ...payload} as IIssue);
      });
  };

  const completeTasks = useMemo(
    () => tasks.filter(task => task.status === 'completed'),
    [tasks],
  );

  if (issueLoading || !issue)
    return (
      <ActivityIndicator
        size={'large'}
        color={Colors.buttonbgcolor}
        style={{marginTop: 100}}
      />
    );
  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingBottom: Platform.OS === 'android' ? 8 : 8,
          paddingHorizontal: sizes.paddingHorizontal,
        }}>
        {/* Top section: Status + View Details */}
        <View style={appStyles.flexSpaceBetween}>
          <View style={[appStyles.flexRow, {gap: 6}]}>
            <View
              style={[
                styles.statusContainer,
                {backgroundColor: getColorByStatus(issue?.status)},
              ]}>
              <Text style={styles.statusText}>{issue?.status}</Text>
            </View>
            {/* PRIORITY */}
            {issue.priority && (
              <View
                style={[
                  styles.statusContainer,
                  {backgroundColor: Colors.black},
                ]}>
                <Text style={styles.statusText}>{issue?.priority}</Text>
              </View>
            )}
            {/* TIME LEFT */}
            {issue?.durationTimeStamp && (
              <View
                style={[styles.statusContainer, {backgroundColor: '#D21F3C'}]}>
                <Text style={styles.statusText}>
                  {getHoursLeft(issue?.durationTimeStamp).toString() +
                    ' hours Left'}
                </Text>
              </View>
            )}
            {/* STATUS */}
          </View>
          <TouchableOpacity
            hitSlop={16}
            onPress={openSheet}
            style={styles.detailsBtn}>
            <Text style={styles.detailsText}>View Details</Text>
            <ArrowRight width={16} height={10} />
          </TouchableOpacity>
        </View>

        {/* Issue title and note */}
        <Text style={styles.issueTitle}>
          {issue?.categoryStructure?.parentCategory.title}
        </Text>
        <Text style={styles.paragraph}>{issue?.note}</Text>

        {/* Progress bar with percent */}
        <View style={styles.progressRow}>
          <ProgressBar
            containerStyle={styles.progressBar}
            max={tasks.length > 0 ? tasks.length : 1} // avoid divide by 0
            value={tasks.length > 0 ? completeTasks.length : 0} // show 0 progress if no tasks
            showBottomText={false}
            filledStyle={{borderRadius: 0}}
            unfilledStyle={{borderRadius: 0}}
          />
          <Text style={styles.percentText}>
            {tasks.length > 0
              ? `${Math.round((completeTasks.length / tasks.length) * 100)}%`
              : '0%'}
          </Text>
        </View>
        {/* CHANGE TASK STATUS */}
        <SelectorItem
          title={issueStatus ? issueStatus : 'Issue Status'}
          icon={<PriorityIcon />}
          onPress={() => setIssueStatusModal(true)}
          label="Issue Status"
          color={issueStatus ? '#475467' : '#98A2B3'}
          containerStyle={{marginTop: 12}}
        />
        {/* CHOOSE ISSUE PRIORITY */}
        <SelectorItem
          title={issuePriority ? issuePriority : 'Select Priority'}
          icon={<PriorityIcon />}
          onPress={() => setPriorityModal(true)}
          label="Issue Priority"
          color={issuePriority ? '#475467' : '#98A2B3'}
          containerStyle={{marginTop: 12}}
        />
        {/* CHOOSE ISSUE DEAD LINES */}
        <SelectorItem
          title={issueDeadline ? issueDeadline : 'Issue Deadline'}
          icon={<CalendarIcon2 />}
          onPress={() => setDurationModal(true)}
          label="Issue Deadline"
          color={issueDeadline ? '#475467' : '#98A2B3'}
          containerStyle={{marginTop: 8, marginBottom: 12}}
        />

        {/* Assigned user */}
        <UserCard
          imageUrl={user?.profileImage?.url || ''}
          name={user?.name || ''}
          date={issue?.createdAt || ''}
          userRole="Client"
        />

        <HorizontalLine marginTop={10} />

        {/* ISSUE RELEVENT TASKS */}
        <Text style={styles.sectionTitle}>Relevant Tasks</Text>

        {tasks.length ? (
          tasks.map((item, index) => (
            <View key={index}>
              <ManagerTaskCard
                timeLeft={
                  getHoursLeft(item.durationTimeStamp).toString() +
                  ' hours Left'
                }
                priority={item.priority}
                status={item.status}
                title={item.title}
                note={item.note}
                dueDate={item.durationTimeStamp}
                containerStyle={{marginBottom: 12}}
                onPressManagerIssueCard={(staffUser?: IUser | null) =>
                  navigation.navigate('TaskDetails', {
                    taskDetails: item,
                    staffUser: staffUser || undefined,
                  })
                }
                staffId={item.staffId}
              />
            </View>
          ))
        ) : (
          <Text style={styles.emptyText}>
            You don’t have any relevant tasks. Click below to create one!
          </Text>
        )}
      </ScrollView>
      {/* CREATE ISSUE TASK  */}
      <View style={appStyles.bottomButtonContainer}>
        <Button
          title="Create Task"
          containerStyle={{marginBottom: insets.bottom}}
          onPress={() =>
            issue?.id &&
            issue?.managerId &&
            navigation.navigate('CreateTask', {
              issueId: issue?.id,
              isFromIssueTaskScreen: true,
            })
          }
        />
      </View>
      {/* Bottom Sheet */}
      <IssueDetailsSheet
        ref={issueSheetRef}
        onClose={() => console.log('Sheet closed')}
        issue={issue || []}
      />
      {/* SELECT ISSUE DEAD LINE MODAL */}
      <OptionsModal
        visible={durationModalModal}
        title="Select Issue Deadline"
        description="Select the hours for this issue"
        options={[
          {value: '24 hours', id: '24'},
          {value: '48 hours', id: '48'},
          {value: '72 hours', id: '72'},
          {value: '96 hours', id: '96'},
        ]}
        selectedValue={issueDeadline}
        onSelect={(data: Option) => setIssueDeadline(data.value)}
        onCancel={() => {
          setIssueDeadline('');
          setDurationModal(false);
        }}
        onRequest={() => {
          setDurationModal(false);
          updateIssue({
            durationTimeStamp: getFutureTimestampFromDuration(issueDeadline),
            duration: issueDeadline,
          });
          navigation.setParams({
            issue: {
              ...issue,
              duration: issueDeadline,
            },
          });
        }}
        isRequestDisabled={issueDeadline ? true : false}
      />
      {/* PRIORITY OPTIONAL MODAL */}
      <OptionsModal
        visible={priorityModal}
        title="Priority"
        description="Select the priority"
        options={[
          {value: 'Low', id: 'Low'},
          {value: 'Medium', id: 'Medium'},
          {value: 'High', id: 'High'},
        ]}
        selectedValue={issuePriority}
        onSelect={(data: Option) => setIssuePriority(data.value)}
        onCancel={() => {
          setIssuePriority('');
          setPriorityModal(false);
        }}
        onRequest={() => {
          setPriorityModal(false);
          updateIssue({priority: issuePriority as 'Low' | 'Medium' | 'High'});
          navigation.setParams({
            issue: {
              ...issue,
              priority: issuePriority as 'Low' | 'Medium' | 'High',
            },
          });
        }}
        isRequestDisabled={issuePriority ? true : false}
      />

      <OptionsModal
        visible={issueStatusModal}
        title="Status"
        description="Change Issue Status"
        options={[
          {value: 'todo', id: 'todo'},
          {value: 'progress', id: 'progress'},
          {value: 'reviewing', id: 'reviewing'},
          {value: 'completed', id: 'completed'},
        ]}
        selectedValue={issueStatus}
        onSelect={(data: Option) => setIssueStatus(data.value)}
        onCancel={() => {
          setIssueStatus('');
          setIssueStatusModal(false);
        }}
        onRequest={() => {
          setIssueStatusModal(false);
          setIssueStatus(issueStatus);
          updateIssue({status: issueStatus});
          navigation.setParams({
            issue: {
              ...issue,
              status: issueStatus,
            },
          });
        }}
        isRequestDisabled={issueStatus ? true : false}
      />
    </View>
  );
};

export default IssueTasks;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingTop: 16,
  },
  statusContainer: {
    backgroundColor: '#F5ED57',
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
  },
  statusText: {
    color: Colors.white,
    fontFamily: Fonts.Medium,
    fontSize: 10,
    textTransform: 'capitalize',
  },
  detailsBtn: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 3,
  },
  detailsText: {
    color: '#0386AC',
    fontSize: 12,
    fontFamily: Fonts.Medium,
  },
  issueTitle: {
    color: Colors.buttonbgcolor,
    paddingTop: 12,
    fontFamily: Fonts.Semibold,
    fontSize: 14,
  },
  paragraph: {
    fontSize: 10,
    color: '#7B7B7B',
    fontFamily: Fonts.Medium,
    lineHeight: 18,
    paddingTop: 4,
  },
  progressRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  progressBar: {
    width: '91%',
  },
  percentText: {
    color: Colors.primary,
    fontFamily: Fonts.Semibold,
    fontSize: 10,
  },
  sectionTitle: {
    fontSize: 12,
    color: '#000B23',
    fontFamily: Fonts.Semibold,
    marginTop: 22,
    marginBottom: 12,
  },
  emptyText: {
    textAlign: 'center',
    fontFamily: Fonts.Semibold,
    color: Colors.placeholder,
    fontSize: 12,
  },
});
