import React from 'react';
import {Text, StyleSheet, View, Image} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {images} from '../../../assets/images';
import {Button} from '../../../component/Button/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamsList} from '../../../navigation/AuthStackNavigation';
type Props = NativeStackScreenProps<AuthStackParamsList, 'RecoverPassword'>;

const PasswordRecovered: React.FC<Props> = ({navigation}) => {
  return (
    <View style={styles.container}>
      <Image style={styles.imgStyle} source={images.recoverpasswordicon} />
      <Text style={styles.titleStyle}>Password Recovered</Text>
      <Text style={styles.subTitle}>
        The password has been successfully recovered, you can log in back with a
        new password
      </Text>

      <View style={{marginTop: 90}}>
        <Button
          title={'Back to Sign in'}
          onPress={() => navigation.navigate('Signin')}
          containerStyle={{backgroundColor: Colors.white, marginHorizontal: 10}}
          textContainer={{color: Colors.buttonbgcolor}}
          // onPress={formik.handleSubmit}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.buttonbgcolor,
    paddingHorizontal: 17,
    // paddingVertical: 22,
  },
  imgStyle: {
    height: 240,
    width: 240,
    alignSelf: 'center',
    marginTop: 145,
  },
  titleStyle: {
    fontSize: 20,
    fontFamily: Fonts.Bold,
    alignSelf: 'center',
    color: Colors.white,
    marginTop: 60,
  },
  subTitle: {
    fontSize: 14,
    fontFamily: Fonts.Medium,
    lineHeight: 22,
    textAlign: 'center',
    color: Colors.subTextlightColor,
    // width:'95%',
    // justifyContent:'flex-start',
  },
});

export default PasswordRecovered;
