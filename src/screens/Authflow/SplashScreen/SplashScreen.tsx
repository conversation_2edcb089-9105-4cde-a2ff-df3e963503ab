import React from 'react';
import {Text, StyleSheet, View, Image, SafeAreaView} from 'react-native';
import {images} from '../../../assets/images';
import {Button} from '../../../component/Button/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamsList} from '../../../navigation/AuthStackNavigation';
import {Colors, Fonts} from '../../../utilities/theme/theme';
type Props = NativeStackScreenProps<AuthStackParamsList, 'Splash'>;

const Splash: React.FC<Props> = ({navigation}) => {
  return (
    <View style={styles.container}>
      <SafeAreaView />
      {/* {-----LOGO ------} */}
      <Image
        style={styles.logoStyle}
        source={images.logo}
        resizeMode="contain"
      />
      <Image
        style={styles.splashImg}
        source={images.splashimg}
        resizeMode="contain"
      />
      <Text style={styles.titleStyle}>Task</Text>
      <Text style={styles.titleStyle2}>Management</Text>

      <View style={{marginTop: 165}}>
        <Button
          title={'Let’s Start'}
          onPress={() => navigation.navigate('Signin')}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: Colors.background,
  },
  titleStyle: {
    color: Colors.primary,
    fontSize: 32,
    fontFamily: Fonts.Semibold,
    marginTop: 12,
    lineHeight: 48,
  },
  titleStyle2: {
    color: Colors.buttonbgcolor,
    fontSize: 34,
    fontFamily: Fonts.Semibold,
    lineHeight: 51,
  },
  logoStyle: {
    width: 136,
    height: 44,
    marginTop: 37,
  },
  splashImg: {
    width: 327,
    height: 212,
    alignSelf: 'center',
    marginTop: 53,
  },
});

export default Splash;
