import React, {useState} from 'react';
import {Text, StyleSheet, View, ScrollView} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import Input from '../../../component/Input/Input';
import {images} from '../../../assets/images';
import {Button} from '../../../component/Button/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamsList} from '../../../navigation/AuthStackNavigation';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import auth from '@react-native-firebase/auth';
import Toast from 'react-native-toast-message';
type Props = NativeStackScreenProps<AuthStackParamsList, 'Forgotpassword'>;

const Forgotpassword: React.FC<Props> = ({navigation}) => {
  const [isLoading, setIsLoading] = useState(false);

  const validationSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email').required('Email is required'),
  });

  const formik = useFormik({
    initialValues: {
      email: '',
    },

    validationSchema: validationSchema,
    onSubmit: values => {
      forgotPassword(values.email);
    },
  });

  const forgotPassword = async (values: string) => {
    setIsLoading(true);
    try {
      await auth()
        .sendPasswordResetEmail(values)
        .then(() => {
          setTimeout(() => {
            navigation.navigate('RecoverPassword');
          });
        });
    } catch (error) {
      console.log('Error sending password reset email', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Error sending password reset email',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView>
        <Text style={styles.mainTitle}>Forgot Password</Text>
        <Text style={styles.subTitle}>
          Enter the email address for which you forgot the password. We will
          send an OTP to the email.
        </Text>

        <Input
          title="Email Address"
          placeholder={'Enter your email'}
          keyboardType={'email-address'}
          leftIcon={images.emailicon}
          containerStyle={{marginTop: 17}}
          onChangeText={formik.handleChange('email')}
          value={formik.values.email}
          onBlur={formik.handleBlur('email')}
        />
        {formik.touched.email && formik.errors.email ? (
          <Text style={styles.errorText}>{formik.errors.email}</Text>
        ) : null}

        <View style={{marginTop: 140}}>
          <Button
            title={'Send OTP'}
            onPress={formik.handleSubmit}
            isLoading={isLoading}
            disabled={isLoading}
          />
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: Colors.background,
  },
  mainTitle: {
    fontSize: 20,
    fontFamily: Fonts.Bold,
    color: Colors.primary,
    paddingTop: 26,
  },
  subTitle: {
    fontSize: 14,
    fontFamily: Fonts.Medium,
    color: Colors.lightcolor,
    lineHeight: 21,
    paddingTop: 13,
  },
  errorText: {
    color: Colors.red,
    fontSize: 12,
    marginTop: 5,
  },
});

export default Forgotpassword;
