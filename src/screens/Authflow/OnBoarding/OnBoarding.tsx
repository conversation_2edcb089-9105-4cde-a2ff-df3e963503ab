import React, {useRef, useState, useEffect, useContext} from 'react';
import {
  View,
  SafeAreaView,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ImageBackground,
  Animated,
} from 'react-native';
import AppIntroSlider from 'react-native-app-intro-slider';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamsList} from '../../../navigation/AuthStackNavigation';
import {images} from '../../../assets/images';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {Button} from '../../../component/Button/Button';
import {AuthContext} from '../../../../App';

type Props = NativeStackScreenProps<AuthStackParamsList, 'OnBoarding'>;

const data = [
  {
    id: 1,
    title: 'Lorem Ipsum',
    text: 'The video call feature can be accessed from anywhere in your house to help you.',
    image: images.OnBoardImage1,
  },
  {
    id: 2,
    title: 'Lorem Ipsum',
    text: `Nobody likes to be alone and the built-in group video call feature helps you connect.`,
    image: images.OnBoardImage2,
  },
  {
    id: 3,
    title: 'Lorem Ipsum',
    text: `While working the app reminds you to smile, laugh, walk and talk with those who matters.`,
    image: images.OnBoardImage3,
  },
];

const OnBoarding: React.FC<Props> = ({navigation}) => {
  const {setFirstLaunch} = useContext(AuthContext);
  const sliderRef = useRef<AppIntroSlider | null>(null);
  const [activeIndex, setActiveIndex] = useState(0);

  const animatedValues = useRef(data.map(() => new Animated.Value(0))).current;

  useEffect(() => {
    animatedValues.forEach((value, index) => {
      Animated.timing(value, {
        toValue: index === activeIndex ? 1 : 0,
        duration: 300,
        useNativeDriver: false,
      }).start();
    });
  }, [activeIndex]);

  const handleStartedbtn = async () => {
    console.log('presss');
    await AsyncStorage.setItem('appLaunched', 'false');
    setFirstLaunch(false);
    navigation.replace('Splash');
  };

  const renderItem = ({item}: {item: (typeof data)[0]}) => (
    <View style={styles.slide}>
      <Image source={images.logo} style={styles.logo} resizeMode="contain" />
      <ImageBackground
        source={item.image}
        style={styles.image}
        resizeMode="cover"
      />
      <Text style={styles.title}>{item.title}</Text>
      <Text style={styles.text}>{item.text}</Text>
    </View>
  );

  const renderPagination = () => (
    <View style={styles.paginationContainer}>
      <View style={styles.paginationDots}>
        {data.map((item, i) => {
          const animatedWidth = animatedValues[i].interpolate({
            inputRange: [0, 1],
            outputRange: [10, 30],
          });
          const animatedColor = animatedValues[i].interpolate({
            inputRange: [0, 1],
            outputRange: [`${Colors.buttonbgcolor}40`, Colors.buttonbgcolor],
          });

          return (
            <TouchableOpacity
              hitSlop={4}
              key={item.id}
              onPress={() => sliderRef.current?.goToSlide(i, true)}>
              <Animated.View
                style={[
                  styles.dot,
                  {
                    width: animatedWidth,
                    backgroundColor: animatedColor,
                  },
                ]}
              />
            </TouchableOpacity>
          );
        })}
      </View>
      <Button
        title="Get Started"
        containerStyle={styles.buttonWrapper}
        textContainer={{lineHeight: 22}}
        onPress={() => {
          if (activeIndex < data.length - 1) {
            sliderRef.current?.goToSlide(activeIndex + 1, true);
          } else {
            handleStartedbtn();
          }
        }}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <SafeAreaView />
      <AppIntroSlider
        keyExtractor={item => item.id.toString()}
        renderItem={renderItem}
        renderPagination={renderPagination}
        data={data}
        ref={sliderRef}
        onSlideChange={index => setActiveIndex(index)}
      />
    </View>
  );
};

export default OnBoarding;

const {height} = Dimensions.get('screen');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.white,
    paddingTop: 32,
  },
  slide: {
    flex: 1,
    paddingHorizontal: 20,
  },
  logo: {
    width: 170,
    height: 55,
    alignSelf: 'center',
  },
  image: {
    width: '100%',
    height: height / 3.2,
    borderRadius: 16,
    overflow: 'hidden',
    marginTop: 12,
  },
  title: {
    fontSize: 24,
    textAlign: 'center',
    color: '#262626',
    fontFamily: Fonts.Bold,
    marginTop: height / 40,
  },
  text: {
    color: '#A6A6A6',
    textAlign: 'center',
    fontFamily: Fonts.Regular,
    fontSize: 16,
    marginTop: 6,
    paddingHorizontal: 32,
  },
  paginationContainer: {
    position: 'absolute',
    bottom: height / 18,
    justifyContent: 'center',
    left: 0,
    right: 0,
  },
  paginationDots: {
    height: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  dot: {
    height: 8,
    borderRadius: 15,
  },
  buttonWrapper: {
    width: '62%',
    alignSelf: 'center',
    borderRadius: 12,
    marginTop: height / 20,
  },
});
