import React from 'react';
import {Text, StyleSheet, View} from 'react-native';
import {Colors, Fonts} from '../../../utilities/theme/theme';
import {Button} from '../../../component/Button/Button';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {AuthStackParamsList} from '../../../navigation/AuthStackNavigation';
import OtpInputs from 'react-native-otp-inputs';
import Clipboard from '@react-native-clipboard/clipboard';

type Props = NativeStackScreenProps<AuthStackParamsList, 'VerificationCode'>;

const VerificationCode: React.FC<Props> = ({navigation}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.mainTitle}>Verification code</Text>
      <Text style={styles.subTitle}>
        Enter verification code sent to your entered email address.
      </Text>

      <OtpInputs
        handleChange={code => ''}
        numberOfInputs={4}
        style={{
          flexDirection: 'row',
          justifyContent: 'space-between',
        }}
        inputContainerStyles={styles.inputContainer}
        focusStyles={styles.focusStyles}
        inputStyles={styles.inputStyles}
        textAlignVertical="center"
        autofillFromClipboard
        textAlign="center"
      />

      <View style={{marginTop: 380}}>
        <Button
          title={'Verify'}
          onPress={() => navigation.navigate('CreatePassword')}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 24,
    backgroundColor: Colors.background,
  },
  mainTitle: {
    fontSize: 20,
    fontFamily: Fonts.Bold,
    color: Colors.primary,
    paddingTop: 26,
  },
  subTitle: {
    fontSize: 14,
    fontFamily: Fonts.Medium,
    color: Colors.lightcolor,
    lineHeight: 21,
    paddingTop: 13,
  },

  inputStyles: {
    color: Colors.primary,
    fontSize: 16,
    fontFamily: Fonts.Semibold,
    marginTop: 2,
    lineHeight: 22,
  },

  inputContainer: {
    borderWidth: 1,
    width: 61,
    height: 48,
    marginTop: 40,
    marginHorizontal: 8,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  focusStyles: {borderColor: Colors.buttonbgcolor},
});

export default VerificationCode;
