import React, {createContext, useEffect, useState} from 'react';
import {View, StyleSheet, ActivityIndicator, StatusBar} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import RootNavigation from './src/navigation/RootNavigation';
import {Colors} from './src/utilities/theme/theme';
import {IUser} from './src/interfaces/IUser';
import firestore from '@react-native-firebase/firestore';
import Toast from 'react-native-toast-message';
import SplashScreen from 'react-native-splash-screen';
import {GestureHandlerRootView} from 'react-native-gesture-handler';

interface AuthContextType {
  userId: string;
  setUserId: (id: string) => void;
  userData: IUser;
  setUserData: (data: IUser) => void;
  firstLaunch: boolean;
  setFirstLaunch: (val: boolean) => void;
}

export const AuthContext = createContext<AuthContextType>({
  userId: '',
  setUserId: () => null,
  userData: {
    name: '',
    email: '',
    id: '',
    userType: 'user',
    profileImage: {
      url: '',
      ref: '',
    },
    progress: 0,
  },
  setUserData: () => null,
  firstLaunch: true,
  setFirstLaunch: () => null,
});

const App = () => {
  const [userId, setUserId] = useState<string>('');
  const [userData, setUserData] = useState<IUser>({
    name: '',
    email: '',
    id: '',
    userType: 'user',
    profileImage: {
      url: '',
      ref: '',
    },
    progress: 0,
    fcmToken: '',
  });
  const [firstLaunch, setFirstLaunch] = useState(true);
  const [isLoadingSession, setIsLoadingSession] = useState(true);

  const retrieveUserData = async () => {
    try {
      const userId = await AsyncStorage.getItem('userId');
      const fcmToken = await AsyncStorage.getItem('fcmToken');
      const isFirstLaunchDone = await AsyncStorage.getItem('appLaunched');
      if (isFirstLaunchDone === 'false') {
        setFirstLaunch(false);
      } else {
        setFirstLaunch(true);
      }

      if (userId) {
        setUserId(userId);
        const userDoc = await firestore().collection('Users').doc(userId).get();

        if (userDoc.exists) {
          setUserData({
            ...(userDoc?.data() as IUser),
          });
        }
      }
    } catch (error) {
      console.error('Error retrieving user data:', error);
    } finally {
      setIsLoadingSession(false);
    }
  };

  useEffect(() => {
    retrieveUserData();
    setTimeout(() => {
      SplashScreen.hide();
    }, 1000);
  }, [userId]);

  if (isLoadingSession)
    return (
      <View style={styles.activityContainer}>
        <ActivityIndicator size={'large'} color={Colors.buttonbgcolor} />
      </View>
    );

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <AuthContext.Provider
        value={{
          userId,
          setUserId,
          userData,
          setUserData,
          firstLaunch,
          setFirstLaunch,
        }}>
        <StatusBar
          backgroundColor={Colors.background}
          barStyle={'dark-content'}
        />
        <RootNavigation />
        <Toast />
      </AuthContext.Provider>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  activityContainer: {flex: 1, alignItems: 'center', justifyContent: 'center'},
});

export default App;
