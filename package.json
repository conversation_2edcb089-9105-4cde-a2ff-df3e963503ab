{"name": "<PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@gorhom/bottom-sheet": "^5", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-firebase/app": "^20.1.0", "@react-native-firebase/auth": "^20.1.0", "@react-native-firebase/firestore": "^20.1.0", "@react-native-firebase/functions": "^20.1.0", "@react-native-firebase/messaging": "^20.1.0", "@react-native-firebase/storage": "^20.1.0", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/native": "^6.1.17", "@react-navigation/native-stack": "^6.10.0", "formik": "^2.4.6", "react": "18.2.0", "react-native": "0.74.2", "react-native-app-intro-slider": "^4.0.4", "react-native-blob-util": "^ 0.13.14", "react-native-calendar-strip": "^2.2.6", "react-native-circular-progress-indicator": "^4.4.2", "react-native-date-picker": "^5.0.4", "react-native-document-picker": "^9.3.0", "react-native-gesture-handler": "^2.17.1", "react-native-image-crop-picker": "^0.41.2", "react-native-image-viewing": "^0.2.2", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-modal": "^13.0.1", "react-native-otp-inputs": "^7.4.0", "react-native-pager-view": "^6.7.0", "react-native-pdf": "^6.7.5", "react-native-progress": "^5.0.1", "react-native-reanimated": "^3.12.1", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^3.32.0", "react-native-size-matters": "^0.4.2", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.3.0", "react-native-svg-transformer": "^1.4.0", "react-native-tab-view": "^4.0.10", "react-native-toast-message": "^2.2.0", "use-debounce": "^10.0.1", "yup": "^1.4.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.84", "@react-native/eslint-config": "0.74.84", "@react-native/metro-config": "0.74.84", "@react-native/typescript-config": "0.74.84", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}